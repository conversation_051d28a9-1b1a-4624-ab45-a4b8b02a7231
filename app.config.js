const IS_DEV = process.env.APP_VARIANT === "development";
const IS_PREVIEW = process.env.APP_VARIANT === "preview";

// iOS
const getBundleId = () => {
  if (IS_DEV) {
    return "com.sea-flux.sea-flux.dev";
  }

  if (IS_PREVIEW) {
    return "com.sea-flux.sea-flux.preview";
  }

  return "com.sea-flux.sea-flux";
};

// Android
const getPackageId = () => {
  if (IS_DEV) {
    return "com.seaflux.seaflux.dev";
  }

  if (IS_PREVIEW) {
    return "com.seaflux.seaflux.preview";
  }

  return "com.seaflux.seaflux";
};

const getAppName = () => {
  if (IS_DEV) {
    return "Sea-Flux (Dev)";
  }

  if (IS_PREVIEW) {
    return "Sea-Flux (Preview)";
  }

  return "Sea-Flux";
};

const getAppIcon = () => {
  if (IS_DEV) {
    return "./assets/icon-dev.png";
  }

  if (IS_PREVIEW) {
    return "./assets/icon-preview.png";
  }

  return "./assets/icon-prod.png";
};

const getFavicon = () => {
  if (IS_DEV) {
    return "./assets/favicon-dev.png";
  }

  if (IS_PREVIEW) {
    return "./assets/favicon-preview.png";
  }

  return "./assets/favicon-prod.png";
};

const getGoogleServicesFileIOS = () => {
  if (IS_DEV) {
    console.log("Using DEV GoogleService-Info file");
    return "./GoogleService-Info-development.plist";
  }

  if (IS_PREVIEW) {
    console.log("Using PREVIEW GoogleService-Info file");
    return "./GoogleService-Info-preview.plist";
  }

  return "./GoogleService-Info.plist";
};

export default ({ config }) => ({
  ...config,
  expo: {
    name: getAppName(),
    slug: "sea-flux",
    version: "1.0.0",
    orientation: "portrait",
    icon: getAppIcon(),
    userInterfaceStyle: "automatic",
    newArchEnabled: false,
    scheme: "seaflux",
    ios: {
      googleServicesFile: getGoogleServicesFileIOS(),
      supportsTablet: true,
      bundleIdentifier: getBundleId(),
      infoPlist: {
        ITSAppUsesNonExemptEncryption: false,
        NSAppTransportSecurity: {
          NSAllowsArbitraryLoads: true
        }
      },
    },
    android: {
      googleServicesFile: "./google-services.json",
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#ffffff",
      },
      package: getPackageId(),
    },
    updates: {
      url: "https://u.expo.dev/5c754462-5c08-4517-bcc5-2a22a590954e",
    },
    web: {
      bundler: "metro",
      favicon: getFavicon(),
    },
    splash: {
      image: "./assets/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff",
    },
    plugins: [
      "expo-asset",
      "expo-font",
      "expo-secure-store",
      "react-native-edge-to-edge",
      "@react-native-firebase/app",
      "@react-native-firebase/auth",
      [
        "expo-build-properties",
        {
          ios: {
            useFrameworks: "static",
          },
        },
      ],
      "expo-router",
      [
        "@sentry/react-native/expo",
        {
          url: "https://sentry.io/",
          project: "sea-flux",
          organization: "sea-flux",
        },
      ],
      [
        "expo-image-picker",
        {
          photosPermission:
            "Sea-Flux needs access to your photos to upload images.",
          cameraPermission:
            "Sea-Flux needs access to your camera to take photos.",
        },
      ],
    ],
    extra: {
      eas: {
        projectId: "5c754462-5c08-4517-bcc5-2a22a590954e",
      },
    },
    owner: "sea-flux",
  },
});
