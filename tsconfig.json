{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "experimentalDecorators": true, // Needed for dependency injection
    "emitDecoratorMetadata": true, // Needed for dependency injection
    "moduleResolution": "bundler",
    "strict": true,
    "module": "esnext", // or "es2020"
    "jsx": "react-native",
    "baseUrl": ".",
    "paths": {
      "@src/*": ["./src/*"],
      "@assets/*": ["./assets/*"],
      "@firebase/auth": ["./node_modules/@firebase/auth/dist/index.rn.d.ts"]
      // "firebase/auth": ["./node_modules/firebase/auth/dist/index.rn.d.ts"],
      // "@src/lib/firebase/services/firestore.service": ["./node_modules/firebase/firestore/dist/index.rn.d.ts"]
    },
    "typeRoots": ["./node_modules/@types", "./src/types"]
  },
  "expo": {
    "resolvePlatformExtension": true
  }
}
