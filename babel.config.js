// eslint-disable-next-line no-undef
module.exports = function (api) {
  api.cache(true);
  return {
    presets: ["babel-preset-expo"],
    plugins: [
      "babel-plugin-transform-typescript-metadata",
      ["@babel/plugin-proposal-decorators", { legacy: true }],
      ["@babel/plugin-transform-class-properties", { loose: true }],
      "react-native-reanimated/plugin",
      ["babel-plugin-react-docgen-typescript", { exclude: "node_modules" }],
      [
        "module-resolver",
        {
          root: ["./src"],
          alias: {
            "@src": "./src",
            "@assets": "./assets",
          },
        },
      ],
      "@babel/plugin-transform-class-static-block"
    ],
  };
};
