{"name": "sea-flux", "version": "1.0.0", "main": "index.tsx", "scripts": {"start": "eas env:pull development && expo start --dev-client --clear", "start:staging": "eas env:pull preview && expo start --dev-client --clear", "build:ios-simulator:dev": "eas build --profile ios-simulator --platform ios", "build:ios:dev": "eas env:pull development --non-interactive && eas build --profile development --platform ios --clear-cache", "build:ios:preview": "eas env:pull preview --non-interactive && eas build --profile preview --platform ios --clear-cache", "build:ios:production": "eas env:pull production --non-interactive && eas build --profile production --platform ios --clear-cache", "build:android:dev": "eas env:pull development --non-interactive && eas build --profile development --platform android --clear-cache", "build:android:preview": "eas env:pull preview --non-interactive && eas build --profile preview --platform android --clear-cache", "build:android:production": "eas env:pull production --non-interactive && eas build --profile production --platform android --clear-cache", "deploy:web:development": "eas env:pull development --non-interactive && npx expo export --platform web --clear && eas deploy --alias dev", "deploy:web:staging": "eas env:pull preview --non-interactive && npx expo export --platform web --clear && eas deploy --alias staging", "deploy:web:production": "eas env:pull production --non-interactive && npx expo export --platform web --clear && eas deploy --prod", "storybook:web": "storybook dev -p 6006", "build-storybook": "storybook build", "storybook-generate": "sb-rn-get-stories --config-path .rnstorybook", "storybook": "cross-env EXPO_PUBLIC_STORYBOOK_ENABLED='true' expo start", "storybook:eas:ios": "cross-env EXPO_PUBLIC_STORYBOOK_ENABLED='true' expo start --ios", "storybook:eas:android": "cross-env EXPO_PUBLIC_STORYBOOK_ENABLED='true' expo start --android", "storybook:ios": "cross-env EXPO_PUBLIC_STORYBOOK_ENABLED='true' expo run:ios", "storybook:android": "cross-env EXPO_PUBLIC_STORYBOOK_ENABLED='true' expo run:android", "prepare": "husky", "prettier": "prettier --write \"./src/**/*.{js,jsx,ts,tsx}\""}, "lint-staged": {"*.{js,ts,tsx}": ["prettier --write", "eslint"]}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@gorhom/bottom-sheet": "^5.1.2", "@lexical/react": "0.11.1", "@lexical/rich-text": "0.11.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "4.5.5", "@react-native-firebase/analytics": "^21.5.0", "@react-native-firebase/app": "^21.12.3", "@react-native-firebase/auth": "^21.12.3", "@react-native-firebase/firestore": "^21.12.3", "@react-native-firebase/functions": "^21.12.3", "@react-native-firebase/storage": "^21.12.3", "@react-navigation/bottom-tabs": "^7.1.1", "@react-navigation/drawer": "^7.1.1", "@react-navigation/elements": "^2.3.0", "@react-navigation/native": "^7.0.12", "@react-navigation/native-stack": "^7.2.0", "@sentry/react-native": "~6.10.0", "@types/lodash": "^4.17.17", "@types/node": "^22.15.15", "babel-plugin-transform-typescript-metadata": "^0.3.2", "expo": "^52.0.40", "expo-application": "~6.0.2", "expo-asset": "~11.0.1", "expo-build-properties": "~0.13.2", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.4", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-image-picker": "~16.0.6", "expo-linking": "~7.0.5", "expo-router": "~4.0.21", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.13", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.4", "expo-updates": "~0.27.4", "firebase": "11.3.1", "formik": "^2.4.6", "getenv": "^1.0.0", "install": "^0.13.0", "inversify": "^7.0.0-alpha.5", "lexical": "0.11.1", "lodash": "^4.17.21", "luxon": "^3.5.0", "metro-transform-plugins": "^0.82.1", "react": "18.3.1", "react-device-detect": "^2.2.3", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-blob-util": "^0.22.2", "react-native-edge-to-edge": "^1.1.3", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-pdf": "^6.7.7", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-signature-canvas": "^4.7.2", "react-native-svg": "15.8.0", "react-native-web": "^0.19.13", "react-native-webview": "13.12.5", "react-pdf": "^9.2.1", "react-signature-canvas": "^1.1.0-alpha.1", "reflect-metadata": "^0.2.2", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@chromatic-com/storybook": "^3", "@eslint/js": "^9.24.0", "@expo/metro-runtime": "~4.0.1", "@storybook/addon-actions": "^8.5.3", "@storybook/addon-controls": "^8.5.3", "@storybook/addon-essentials": "^8.6.9", "@storybook/addon-links": "^8.5.3", "@storybook/addon-onboarding": "^8.6.9", "@storybook/addon-ondevice-actions": "^8.5.4", "@storybook/addon-ondevice-backgrounds": "^8.5.4", "@storybook/addon-ondevice-controls": "^8.5.4", "@storybook/addon-ondevice-notes": "^8.5.4", "@storybook/blocks": "^8.6.9", "@storybook/core": "^8.5.3", "@storybook/experimental-addon-test": "^8.6.10", "@storybook/react": "^8.6.9", "@storybook/react-native": "^8.6.2", "@storybook/react-native-web-vite": "^8.6.10", "@storybook/test": "^8.6.9", "@types/lodash": "^4.17.17", "@types/luxon": "^3.4.2", "@types/react": "~18.3.12", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "@vitest/browser": "^3.0.9", "@vitest/coverage-v8": "^3.0.9", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-react-docgen-typescript": "^1.5.1", "babel-plugin-react-native-web": "^0.19.10", "babel-preset-expo": "^12.0.9", "cross-env": "^7.0.3", "danger": "^13.0.4", "eslint": "^9.24.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.0", "playwright": "^1.51.1", "prettier": "^3.5.3", "prop-types": "^15.8.1", "storybook": "^8.6.12", "typescript": "^5.7.2", "typescript-eslint": "^8.29.1", "vite": "^6.2.5", "vitest": "^3.0.9"}, "resolutions": {"react-docgen-typescript": "2.2.2"}, "overrides": {"react-docgen-typescript": "2.2.2"}, "pnpm": {"overrides": {"react-docgen-typescript": "2.2.2"}}, "private": true}