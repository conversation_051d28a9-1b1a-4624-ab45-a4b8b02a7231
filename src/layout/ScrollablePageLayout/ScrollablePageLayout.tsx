import React from "react";
import { FlatList, FlatListProps } from "react-native";

interface ScrollablePageLayoutProps<T>
  extends Omit<FlatListProps<T>, "data" | "renderItem" | "keyExtractor"> {}

export const ScrollablePageLayout = <T extends any>({
  children,
  ...props
}: ScrollablePageLayoutProps<T>) => {
  return (
    <FlatList
      {...props}
      data={["pageLayout"] as any[]}
      renderItem={() => (
        <>
          {/** The page content is loaded here */}
          {children}
        </>
      )}
    />
  );
};
