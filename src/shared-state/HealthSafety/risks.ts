import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from "@src/lib/firebase/services/firestore.service";
import { firestore, setupArrayQueryListener } from "@src/lib/firebase";
import {
  CreateableDocument,
  SharedStateConfig,
  UpdateableDocument,
  sharedState,
} from "@src/shared-state/shared-state";
import { registerFiles } from "@src/shared-state/FileSyncSystem/filesToCache";
import { canView } from "@src/shared-state/Core/userPermissions";
import { renderCategoryName } from "@src/lib/categories";

//
// Risk Registry (v2 of Hazard Register)
// Will only load if licenseeSettings.riskRegister.version === 2
//

export interface Control {
  id: string;
  description: string;
  likelihood: string;
  consequence: string;
  // Other properties as needed
}

export interface Risk extends CreateableDocument, UpdateableDocument {
  categoryId: string;
  controls: string;
  deletedBy?: string;
  files?: string[];
  interval: string;
  name: string;
  postControls: Control;
  preControls: Control;
  risks: string;
  shouldReportToManagement?: boolean;
  state: "active" | "deleted";
  vesselIds: string[];
  whenDeleted?: number;
  dateDue: string;
  dateLastReviewed: string;
  whoResponsible?: string;
  // Generated at run time - not in original data
  searchText?: string;
}

export type RisksData = {
  all: Risk[];
  byId: {
    [hazardId: string]: Risk;
  };
  byVesselId: {
    [vesselId: string]: {
      byCategoryId: {
        // Correctly represent the nested structure here
        [categoryId: string]: Risk[];
      };
    };
  };
  byCategoryId: {
    [categoryId: string]: Risk[]; // Alphabetical list
  };
};

export const risksConfig: SharedStateConfig<RisksData> = {
  isAlwaysActive: false,
  dependencies: [
    "vesselIds",
    "licenseeSettings",
    "riskCategories",
    "userPermissions",
  ],
  countLiveDocs: () =>
    Object.keys(sharedState.risks.current?.byId ?? {}).length,
  run: (done, set, clear) => {
    clear();
    const vesselIds = sharedState.vesselIds.current;
    const licenseeSettings = sharedState.licenseeSettings.current;
    const riskCategories = sharedState.riskCategories.current;
    if (
      licenseeSettings?.riskRegister?.version === 2 &&
      vesselIds &&
      vesselIds.length > 0 &&
      riskCategories &&
      canView("hazardRegister")
    ) {
      const searchText = (risk: Risk) => {
        let s = risk.name;
        if (risk.categoryId)
          s += renderCategoryName(risk.categoryId, riskCategories);
        if (risk.risks) s += risk.risks;
        if (risk.controls) s += risk.controls;
        if (risk.whoResponsible) s += risk.whoResponsible;
        return s.toLowerCase();
      };

      return setupArrayQueryListener(
        "risks", // what
        collection(firestore, "risks"),
        [where("state", "==", "active")], // baseConstraints
        "vesselIds",
        "array-contains-any",
        vesselIds,
        [orderBy("name", "asc") as QueryOrderByConstraint],
        (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
          // processDocs
          done();

          const all = docs.map((doc) => {
            return {
              ...doc.data(),
              id: doc.id,
              searchText: searchText(doc.data() as Risk),
            } as Risk;
          });

          if (isCombined) {
            // Need to sort by name
            all.sort((a, b) => {
              return a.name.localeCompare(b.name);
            });
          }

          const byId = {} as {
            [id: string]: Risk;
          };
          const byVesselId = {} as {
            [vesselId: string]: {
              byCategoryId: {
                [categoryId: string]: Risk[];
              };
            };
          };
          const byCategoryId = {} as {
            [categoryId: string]: Risk[];
          };

          all.forEach((risk: Risk) => {
            byId[risk.id] = risk;
            registerFiles(risk.files, "risks", risk);
            if (byCategoryId[risk.categoryId] === undefined) {
              byCategoryId[risk.categoryId] = [];
            }
            byCategoryId[risk.categoryId].push(risk);

            risk.vesselIds.forEach((vesselId: string) => {
              if (byVesselId[vesselId] === undefined) {
                byVesselId[vesselId] = {
                  byCategoryId: {},
                };
              }
              const vessel = byVesselId[vesselId];
              if (vessel.byCategoryId[risk.categoryId] === undefined) {
                vessel.byCategoryId[risk.categoryId] = [];
                // vessel.categories.push(risk.category);
              }
              vessel.byCategoryId[risk.categoryId].push(risk);
            });
          });

          set({
            all,
            byId,
            byVesselId,
            byCategoryId,
          });
        },
        (error) => {
          done();
        },
      );
    }
  },
};
