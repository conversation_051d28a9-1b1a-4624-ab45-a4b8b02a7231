import { SharedStateConfig, sharedState } from "@src/shared-state/shared-state";
import { CategoriesData, onCategoriesSnapshot } from "@src/lib/categories";

/**
 * Loads safetyCheckCategories
 */
export const safetyCheckCategoriesConfig: SharedStateConfig<CategoriesData> = {
  isAlwaysActive: false,
  dependencies: ["vesselId"],
  countLiveDocs: () =>
    sharedState.safetyCheckCategories.current?.ids.length ?? 0,
  run: (done, set, clear) => {
    clear();
    const vesselId = sharedState.vesselId.current;
    if (vesselId) {
      return onCategoriesSnapshot(
        "safetyCheckCategories",
        "vesselId",
        vesselId,
        (data: CategoriesData) => {
          // onLoaded
          done();
          set(data);
        },
        (error) => {
          // onError
          done();
          console.log(`Error getting safetyCheckCategories`, error);
        },
      );
    } else {
      done();
    }
  },
};
