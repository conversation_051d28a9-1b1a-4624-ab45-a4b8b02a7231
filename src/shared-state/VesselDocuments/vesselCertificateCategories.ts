import { SharedStateConfig, sharedState } from "@src/shared-state/shared-state";
import { CategoriesData, onCategoriesSnapshot } from "@src/lib/categories";

/**
 * Loads vesselCertificateCategories
 */
export const vesselCertificateCategoriesConfig: SharedStateConfig<CategoriesData> =
  {
    isAlwaysActive: false,
    dependencies: ["vesselId"],
    countLiveDocs: () =>
      sharedState.vesselCertificateCategories.current?.ids.length ?? 0,
    run: (done, set, clear) => {
      clear();
      const vesselId = sharedState.vesselId.current;
      if (vesselId) {
        return onCategoriesSnapshot(
          "vesselCertificateCategories",
          "vesselId",
          vesselId,
          (data: CategoriesData) => {
            // onLoaded
            done();
            set(data);
          },
          (error) => {
            // onError
            done();
            console.log(`Error getting vesselCertificateCategories`, error);
          },
        );
      } else {
        done();
      }
    },
  };
