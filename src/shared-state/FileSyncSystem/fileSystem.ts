import * as ExpoFileSystem from "expo-file-system";
import { Platform } from "react-native";

export interface FileInfo {
  name: string;
  type: "directory" | "file";
  size: number;
  ctime?: number;
  mtime: number;
  uri: string;
}

export enum Directory {
  Documents = "DOCUMENTS",
  Data = "DATA",
  Library = "LIBRARY",
  Cache = "CACHE",
  External = "EXTERNAL",
  ExternalStorage = "EXTERNAL_STORAGE",
}

export const Filesystem = {
  async mkdir({
    path,
    directory,
    recursive,
  }: {
    path: string;
    directory: string;
    recursive?: boolean;
  }): Promise<void> {
    if (Platform.OS === "web") {
      // For web, we'll create a virtual directory structure in IndexedDB
      const request = indexedDB.open("Filesystem", 1);

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains("directories")) {
          db.createObjectStore("directories", { keyPath: "path" });
        }
        if (!db.objectStoreNames.contains("files")) {
          db.createObjectStore("files", { keyPath: "path" });
        }
      };

      return new Promise((resolve, reject) => {
        request.onerror = () => {
          console.error(
            "[Filesystem.mkdir] Failed to open database:",
            request.error,
          );
          reject(request.error);
        };
        request.onsuccess = () => {
          const db = request.result;
          // Ensure both stores exist before creating transaction
          if (!db.objectStoreNames.contains("directories")) {
            console.error("[Filesystem.mkdir] Directories store not found");
            return reject(new Error("Directories store not found"));
          }

          const tx = db.transaction("directories", "readwrite");
          const store = tx.objectStore("directories");

          const fullPath = `${directory}${path}`;
          const pathParts = fullPath.split("/").filter(Boolean);

          // If recursive, create all parent directories
          if (recursive) {
            let currentPath = "";
            pathParts.forEach((part) => {
              currentPath += "/" + part;
              store.put({ path: currentPath, created: Date.now() });
            });
          } else {
            store.put({ path: fullPath, created: Date.now() });
          }

          tx.oncomplete = () => {
            resolve();
          };
          tx.onerror = () => {
            console.error("[Filesystem.mkdir] Transaction failed:", tx.error);
            reject(tx.error);
          };
        };
      });
    } else {
      const fullPath = `${directory}${path}`;
      await ExpoFileSystem.makeDirectoryAsync(fullPath, {
        intermediates: recursive,
      })
        .catch((error) => {
          console.error("[Filesystem.mkdir] Failed to make directory", error);
        })
        .then(() => {
          // Directory created successfully
        });
    }
  },

  async readdir({
    path,
    directory,
  }: {
    path: string;
    directory: string;
  }): Promise<{ files: { name: string; type: string }[] }> {
    if (Platform.OS === "web") {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open("Filesystem", 1);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
          const db = request.result;
          // Check if stores exist before creating transaction
          if (
            !db.objectStoreNames.contains("files") ||
            !db.objectStoreNames.contains("directories")
          ) {
            console.error("[Filesystem.readdir] Required stores not found");
            return reject(new Error("Required stores not found"));
          }

          const tx = db.transaction(["files", "directories"], "readonly");
          const fileStore = tx.objectStore("files");
          const dirStore = tx.objectStore("directories");

          const fullPath = `${directory}${path}`;
          const files: { name: string; type: string }[] = [];

          // Get all files in this directory
          const fileRange = IDBKeyRange.bound(
            fullPath + "/",
            fullPath + "/\uffff",
          );

          fileStore.openCursor(fileRange).onsuccess = (event) => {
            const cursor = (event.target as IDBRequest).result;
            if (cursor) {
              const filePath = cursor.key as string;
              const fileName = filePath.split("/").pop()!;
              files.push({ name: fileName, type: "file" });
              cursor.continue();
            } else {
              resolve({ files });
            }
          };
        };
      });
    } else {
      const fullPath = `${directory}${path}`;
      const files = await ExpoFileSystem.readDirectoryAsync(fullPath);
      return {
        files: files.map((name) => ({ name, type: "file" })),
      };
    }
  },

  async stat({
    path,
    directory,
  }: {
    path: string;
    directory: string;
  }): Promise<{ size: number; uri: string }> {
    if (Platform.OS === "web") {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open("Filesystem", 1);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
          const db = request.result;
          const tx = db.transaction(["files"], "readonly");
          const store = tx.objectStore("files");

          const fullPath = `${directory}${path}`;
          const getReq = store.get(fullPath);

          getReq.onsuccess = () => {
            const fileData = getReq.result;
            if (!fileData) {
              reject(new Error("File does not exist"));
              return;
            }
            resolve({
              size: fileData.size ?? 0,
              uri: URL.createObjectURL(fileData.blob),
            });
          };
          getReq.onerror = () => reject(getReq.error);
        };
      });
    } else {
      const fullPath = `${directory}${path}`;
      const info = await ExpoFileSystem.getInfoAsync(fullPath);
      if (!info.exists) {
        throw new Error("File does not exist");
      }
      return {
        size: info.size || 0,
        uri: info.uri,
      };
    }
  },

  async rename({
    directory,
    from,
    to,
  }: {
    directory: string;
    from: string;
    to: string;
  }): Promise<void> {
    if (Platform.OS === "web") {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open("Filesystem", 1);
        request.onerror = () => reject(request.error);
        request.onsuccess = async () => {
          const db = request.result;
          const tx = db.transaction(["files", "directories"], "readwrite");
          const fileStore = tx.objectStore("files");
          const dirStore = tx.objectStore("directories");

          const fromPath = `${directory}${from}`;
          const toPath = `${directory}${to}`;

          // Move file data
          const fileData = await new Promise((resolve, reject) => {
            const getReq = fileStore.get(fromPath);
            getReq.onsuccess = () => resolve(getReq.result);
            getReq.onerror = () => reject(getReq.error);
          });
          if (fileData) {
            const updatedFileData = { ...fileData, path: toPath };
            fileStore.put(updatedFileData);
            fileStore.delete(fromPath);
          }

          tx.oncomplete = () => resolve();
          tx.onerror = () => reject(tx.error);
        };
      });
    } else {
      const fromPath = `${directory}${from}`;
      const toPath = `${directory}${to}`;
      await ExpoFileSystem.moveAsync({
        from: fromPath,
        to: toPath,
      });
    }
  },

  async deleteFile({
    path,
    directory,
  }: {
    path: string;
    directory: string;
  }): Promise<void> {
    if (Platform.OS === "web") {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open("Filesystem", 1);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
          const db = request.result;
          const tx = db.transaction(["files"], "readwrite");
          const store = tx.objectStore("files");

          const fullPath = `${directory}${path}`;
          store.delete(fullPath);

          tx.oncomplete = () => resolve();
          tx.onerror = () => reject(tx.error);
        };
      });
    } else {
      const fullPath = `${directory}${path}`;
      await ExpoFileSystem.deleteAsync(fullPath);
    }
  },

  // async writeFile({ path, data, directory }: { path: string, data: string, directory: string }): Promise<void> {
  //     const fullPath = `${directory}${path}`;
  //     await ExpoFileSystem.writeAsStringAsync(fullPath, data);
  // },

  async writeFile({
    path,
    data,
    directory,
    recursive = false,
  }: {
    path: string;
    data: string | Blob;
    directory: string;
    recursive?: boolean;
  }): Promise<{ uri: string }> {
    try {
      const fullPath = `${directory}${path}`;

      if (Platform.OS === "web") {
        if (recursive) {
          // For web/IndexedDB, we don't actually need to create directories
          // since the path is stored as a single string
        }

        return new Promise((resolve, reject) => {
          const request = indexedDB.open("Filesystem", 1);
          request.onerror = () => reject(request.error);
          request.onsuccess = () => {
            const db = request.result;
            const tx = db.transaction(["files"], "readwrite");
            const store = tx.objectStore("files");

            const fileData = {
              path: fullPath,
              content: data,
              timestamp: new Date().getTime(),
            };

            store.put(fileData);

            tx.oncomplete = async () => {
              console.log(
                `[FileSystem] File written successfully: ${fullPath}`,
              );
              const result = await this.getUri({ path, directory });
              resolve(result);
            };
            tx.onerror = () => reject(tx.error);
          };
        });
      } else {
        // Handle directory creation based on recursive flag
        const dirPath = fullPath.split("/").slice(0, -1).join("/");
        if (recursive) {
          await ExpoFileSystem.makeDirectoryAsync(dirPath, {
            intermediates: true,
          });
        }

        if (data instanceof Blob) {
          // Convert blob to base64
          const reader = new FileReader();
          const base64Promise = new Promise<string>((resolve, reject) => {
            reader.onload = () => {
              const base64 = reader.result as string;
              const base64Data = base64.split(",")[1]; // Remove data URL prefix
              resolve(base64Data);
            };
            reader.onerror = reject;
          });
          reader.readAsDataURL(data);
          data = await base64Promise;
          // Write file with additional options
          await ExpoFileSystem.writeAsStringAsync(fullPath, data, {
            encoding: ExpoFileSystem.EncodingType.Base64,
          });
          console.log(`[FileSystem] File written successfully: ${fullPath}`);
          const result = await this.getUri({ path, directory });
          return result;
        } else {
          // Write file with additional options
          await ExpoFileSystem.writeAsStringAsync(fullPath, data, {
            encoding: ExpoFileSystem.EncodingType.Base64,
          });
          console.log(`[FileSystem] File written successfully: ${fullPath}`);
          const result = await this.getUri({ path, directory });
          return result;
        }
      }
    } catch (error) {
      console.error("[FileSystem] Write file error:", error);
      throw error;
    }
  },

  // async readFile({ path, directory }: { path: string, directory: string }): Promise<{ data: string }> {
  //     const fullPath = `${directory}${path}`;
  //     const content = await ExpoFileSystem.readAsStringAsync(fullPath);
  //     return { data: content };
  // },

  // Read file for verification
  async readFile({
    path,
    directory,
  }: {
    path: string;
    directory: string;
  }): Promise<any> {
    const fullPath = `${directory}${path}`;
    try {
      if (Platform.OS === "web") {
        return new Promise((resolve, reject) => {
          const request = indexedDB.open("Filesystem", 1);
          request.onerror = () => reject(request.error);
          request.onsuccess = () => {
            const db = request.result;
            const tx = db.transaction(["files"], "readonly");
            const store = tx.objectStore("files");

            const getRequest = store.get(fullPath);

            getRequest.onsuccess = () => {
              if (getRequest.result) {
                resolve(getRequest.result.content);
              } else {
                reject(new Error("File not found"));
              }
            };
            getRequest.onerror = () => reject(getRequest.error);
          };
        });
      } else {
        const content = await ExpoFileSystem.readAsStringAsync(fullPath, { encoding: "base64" });
        return content;
      }
    } catch (error) {
      console.error("[FileSystem] Read file error:", error);
      throw error;
    }
  },

  async getUri({
    path,
    directory,
  }: {
    path: string;
    directory: string;
  }): Promise<{ uri: string }> {
    if (Platform.OS === "web") {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open("Filesystem", 1);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
          const db = request.result;
          const tx = db.transaction(["files"], "readonly");
          const store = tx.objectStore("files");

          const fullPath = `${directory}${path}`;
          const getRequest = store.get(fullPath);

          getRequest.onsuccess = () => {
            if (getRequest.result) {
              const blob = new Blob([getRequest.result.content], {
                type: "application/octet-stream",
              });
              const uri = URL.createObjectURL(blob);
              resolve({ uri });
            } else {
              reject(new Error("File not found"));
            }
          };
          getRequest.onerror = () => reject(getRequest.error);
        };
      });
    } else {
      const fullPath = `${directory}${path}`;
      const uri = await ExpoFileSystem.getContentUriAsync(fullPath);
      return { uri };
    }
  },

  // Example usage method for demonstration
  async demonstrateFileWrite() {
    try {
      // Determine appropriate directory based on platform
      const directory = Platform.select({
        ios: ExpoFileSystem.documentDirectory,
        android: ExpoFileSystem.documentDirectory,
        web: Directory.Cache,
      })!;

      // Different types of data to write
      const jsonData = JSON.stringify({
        timestamp: Date.now(),
        message: "File system test",
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version,
        },
      });

      const textData = `File System Test
            Timestamp: ${new Date().toISOString()}
            Platform: ${Platform.OS}
            Version: ${Platform.Version}`;

      // Write JSON file
      await this.writeFile({
        path: "test-data-007.json",
        data: jsonData,
        directory,
      });

      // Write text file
      await this.writeFile({
        path: "test-log-007.txt",
        data: textData,
        directory,
      });

      const jsonFileInfoUri = await this.getUri({
        path: "test-data-007.json",
        directory,
      });
      const textFileInfoUri = await this.getUri({
        path: "test-log-007.txt",
        directory,
      });

      console.log("JSON File Info: uri", jsonFileInfoUri);
      console.log("Text File Info: uri", textFileInfoUri);

      const jsonFileInfoRead = await this.readFile({
        path: "test-data-007.json",
        directory,
      });
      const textFileInfoRead = await this.readFile({
        path: "test-log-007.txt",
        directory,
      });

      console.log("JSON File Info: read", jsonFileInfoRead);
      console.log("Text File Info: read", textFileInfoRead);
    } catch (error) {
      console.error("Demonstration failed:", error);
    }
  },
};
