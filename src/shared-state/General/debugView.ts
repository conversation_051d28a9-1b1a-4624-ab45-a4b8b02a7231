import { sharedState, SharedStateConfig } from "@src/shared-state/shared-state";
import { Platform } from "react-native";

//
// Listens for "secret" user interactions that will enable the debugger.
// To activate the debugger, either:
// * Type debug333
// * or click/tap right at the top of the screen 7 times in a row, with each click/tap being further to the right
//
// Once activated, or anytime you're in the development environment, you can open/close the debugger using F2.
//

const debugSecret = "debug333";
let debugSecretIndex = 0;
let debugClicks = 0;
let debugClickX = 0;

export const activateDebugger = () => {
  debugSecretIndex = 0;
  debugClickX = 0;
  debugClicks = 0;
  sharedState.debugView.set((current) => {
    return {
      ...current!,
      isActive: true,
      show: !current!.show,
    };
  });
};

export type DebugViewConfig = {
  isActive: boolean,
  show: boolean;
  tab: string;
};

export const debugViewConfig: SharedStateConfig<DebugViewConfig> = {
  isAlwaysActive: true,
  default: {
    isActive: false,
    show: false,
    tab: "State",
  },
  notes: "Config for the debug panel",
};

export const onScreenClicked = (x: number, y: number) => {
  if (y < 80 && x > debugClickX) {
    debugClickX = x;
    debugClicks++;
    if (debugClicks >= 7) {
      activateDebugger();
    }
  } else {
    debugClickX = 0;
    debugClicks = 0;
  }
};

export const initDebugView = () => {
  if (Platform.OS === 'web') {
    // Web Version

    const keyupListenenr = (event: KeyboardEvent) => {
      if (event.key === "F2" && (sharedState.debugView.current?.isActive || process.env.NODE_ENV === 'development')) {
        activateDebugger();
        return;
      }
    
      if (debugSecret[debugSecretIndex] === event.key) {
        debugSecretIndex++;
        if (debugSecretIndex >= debugSecret.length) {
          activateDebugger();
          return;
        }
      } else {
        debugSecretIndex = event.key === debugSecret[0] ? 1 : 0;
      }
    };

    const mouseupListenener = (event: MouseEvent) => {
      onScreenClicked(event.clientX, event.clientY);
    };

    document.addEventListener("keyup", keyupListenenr);
    document.addEventListener("mouseup", mouseupListenener);

    return () => {
      document.removeEventListener("keyup", keyupListenenr);
      document.removeEventListener("mouseup", mouseupListenener);
    };
  } else {
    // Native Version
    // This is handled via the <SafeAreaView /> within app/_layout.tsx
    return () => undefined;
  }
};
