import { useEffect, useState } from "react";
import { ActionLogEntry, canViewActionLogEntry } from "./actionLog";
import { sharedState } from "@src/shared-state/shared-state";
import { firestore, setupArrayQueryListener } from "@src/lib/firebase";
import {
  collection,
  onSnapshot,
  query,
  DocumentData,
  QueryDocumentSnapshot,
  where,
  orderBy,
  QueryOrderByConstraint,
} from "@src/lib/firebase/services/firestore.service";
import { getDayOffsetMillis } from "@src/lib/datesAndTime";

/**
 * Loads an array of ActionLogEntry given maxDays worth of history.
 * Specifying a vesselId will only load logs for that vessel,
 * otherwise, we'll load
 * - all logs for the vessels this user has access to
 * - and all logs not associated with any vessels (licensee wide logs).
 *
 * Unfortunately, because there are some logs we shouldn't see, we can't limit by number.
 *
 */
export const useActionLog = (
  isActive: boolean,
  maxDays: number, // limit to a number of days of history
  vesselId?: string, // limit to a particular vessel (optional)
  onLoaded?: () => void, // Callback once data has finished loading
  /**
   * Setting this to false is a bad idea because it's possible that only cached data comes through.
   * If we start using allowCached = false, this will need some more work done.
   */
  allowCached = true,
) => {
  const [actionLog, setActionLog] = useState<ActionLogEntry[]>();
  const licenseeId = sharedState.licenseeId.use();

  //console.log(`useActionLog isActive=${isActive} vesselId=${vesselId}`);

  const processDocs = (docs: QueryDocumentSnapshot<DocumentData>[]) => {
    const entries = [] as ActionLogEntry[];
    docs.forEach((doc) => {
      const entry = {
        id: doc.id,
        ...doc.data(),
      } as ActionLogEntry;
      if (canViewActionLogEntry(entry)) {
        entries.push(entry);
      }
    });
    return entries;
  };

  useEffect(() => {
    if (isActive && licenseeId) {
      if (vesselId) {
        // Just for a specific Vessel
        // TODO! Once I can change datastructure of actionLog we should only need one chunk of code...

        return onSnapshot(
          query(
            collection(firestore, "actionLog"),
            where("licenseeId", "==", licenseeId),
            where("vesselIds", "array-contains", vesselId),
            where("when", ">=", getDayOffsetMillis(-maxDays)),
            orderBy("when", "desc"),
          ),
          (snap) => {
            if (!allowCached && snap.metadata.fromCache) {
              console.log(`Ignoring cached actionLogs (vesselId=${vesselId})`);
              return;
            }
            setActionLog(processDocs(snap.docs));
            if (onLoaded) {
              onLoaded();
            }
          },
          (error) => {
            setActionLog(undefined);
            console.log(
              `Error getting action log for vessel ${vesselId}`,
              error.message,
              error,
            );
          },
        );
      } else {
        // Licensee wide
        let vesselsResults = undefined as ActionLogEntry[] | undefined;
        let licenseeResults = undefined as ActionLogEntry[] | undefined;

        const onQueryProcessed = () => {
          if (vesselsResults !== undefined && licenseeResults !== undefined) {
            const results = [...vesselsResults, ...licenseeResults];
            results.sort((a, b) => b.when - a.when);
            setActionLog(results);
            if (onLoaded) {
              onLoaded();
            }
          }
        };

        const cleanupVesselsQuery = setupArrayQueryListener(
          "actionLog", // what
          collection(firestore, "actionLog"),
          [
            where("licenseeId", "==", licenseeId),
            where("when", ">=", getDayOffsetMillis(-maxDays)),
          ],
          "vesselIds",
          "array-contains-any",
          sharedState.vesselIds.current!,
          //[],
          [orderBy("when", "desc") as QueryOrderByConstraint], // orderByConstraints
          (
            docs: QueryDocumentSnapshot<DocumentData>[],
            isCombined: boolean, // (since we sort the final results anyway, this is irrelevant)
          ) => {
            // processDocs
            vesselsResults = processDocs(docs);
            onQueryProcessed();
          },
          (error) => {
            setActionLog(undefined);
            console.log(
              `Error getting action log for all my vessels`,
              error.message,
              error,
            );
          },
        );

        const cleanupLicenseeQuery = onSnapshot(
          query(
            collection(firestore, "actionLog"),
            where("licenseeId", "==", licenseeId),
            where("vesselIds", "==", "any"),
            where("when", ">=", getDayOffsetMillis(-maxDays)),
            orderBy("when", "desc"),
          ),
          (snap) => {
            if (!allowCached && snap.metadata.fromCache) {
              console.log(`Ignoring cached actionLogs (licensee)`);
              return;
            }
            licenseeResults = processDocs(snap.docs);
            onQueryProcessed();
          },
          (error) => {
            setActionLog(undefined);
            console.log(
              `Error getting action log for vessel ${vesselId}`,
              error.message,
              error,
            );
          },
        );

        return () => {
          cleanupVesselsQuery();
          cleanupLicenseeQuery();
        };
      }
    } else {
      setActionLog(undefined);
    }
  }, [allowCached, isActive, licenseeId, maxDays, onLoaded, vesselId]);

  return actionLog;
};
