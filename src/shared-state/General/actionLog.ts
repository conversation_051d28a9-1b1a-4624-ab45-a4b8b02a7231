//
// Loads action log when needed.
// Call logAction to save a new ActionLogEntry.
//

import {
  collection,
  doc,
  firestore,
  serverTimestamp,
  SplittableBatch,
  WriteBatch,
} from "@src/lib/firebase/services/firestore.service";
import { sharedState } from "@src/shared-state/shared-state";
import { onCollectionUpdated } from "@src/shared-state/DataSyncSystem/dataSync";
import { canCreate, canView } from "@src/shared-state/Core/userPermissions";

/**
 * How many days of actionLog history should we have prepared for becoming offline
 */
export const maxOfflineActionLogDays = 7;

const logActionCodes = {
  Add: "A",
  Update: "U",
  Renew: "N",
  Archive: "V",
  Unarchive: "R",
  Delete: "D",
};
type LogActionType = keyof typeof logActionCodes;

export interface ActionLogEntry {
  id?: string;
  action: string;
  collection: string;
  detail: string;
  docId: string;
  licenseeId: string;
  personnelIds?: string[];
  type?: string | string[];
  userId: string;
  vesselIds: string[] | "any";
  when: number;
}

export const logAction = (
  batch: WriteBatch | SplittableBatch,
  actionType: LogActionType,
  _collection: string,
  docId: string,
  detail: string,
  vesselIds?: string[] | undefined,
  personnelIds?: string[] | undefined,
  type?: string | undefined,
) => {
  const ref = doc(collection(firestore, "actionLog"));

  batch.set(
    ref,
    {
      licenseeId: sharedState.licenseeId.current,
      userId: sharedState.userId.current,
      vesselIds: vesselIds ?? "any",
      when: Date.now(),
      collection: _collection,
      docId: docId,
      action: logActionCodes[actionType],
      detail: detail ? detail : "",
      type: type,
      personnelIds: personnelIds,
      touched: serverTimestamp(),
    },
    { merge: true },
  );
  onCollectionUpdated(batch, "actionLog");

  return ref.id;
};

export const canViewActionLogEntry = (action: ActionLogEntry) => {
  const userId = sharedState.userId.current;
  if (!userId) {
    return false;
  }
  switch (action.collection) {
    case "engines":
      return canView("engineHours");
    case "vessels":
      if (action.type) {
        if (action.type === "logbookSettings") return canView("logbook");
        if (action.type === "safetyMeetingSettings")
          return canView("healthSafetyMeetings");
      }
      return canView("vesselSettings");
    case "voyages":
    case "voyageDocuments":
      return canView("logbook");
    case "safetyCheckItems":
    case "safetyCheckCompleted":
      return canView("safetyEquipmentChecks");
    case "safetyEquipmentItems":
    case "safetyEquipmentTaskCompleted":
      return canView("safetyEquipmentList");
    case "hazards":
      return canView("hazardRegister");
    // case 'medicalReports':
    //     return canView('incidentAccidentMedicalRegister');
    case "incidents":
    case "incidentReviews":
      return canView("incidentAccidentMedicalRegister");
    case "correctiveActions":
      return canView("correctiveActions");
    case "drills":
    case "drillReports":
      return canView("drills");
    case "scheduledMaintenanceTasks":
      return canView("maintenanceSchedule");
    case "maintenanceTasksCompleted":
      if (action.type) {
        if (action.type === "scheduled") {
          return (
            canView("maintenanceSchedule") || canView("maintenanceHistory")
          );
        }
        if (action.type === "job") {
          return canView("jobList") || canView("maintenanceHistory");
        }
      }
      return canView("maintenanceHistory");
    case "jobs":
      return canView("jobList");
    case "spareParts":
      return canView("sparePartsList");
    case "vesselCertificates":
      return canView("vesselCertificates");
    case "vesselDocuments":
      return canView("vesselDocuments");
    case "surveyReports":
      return canView("survey");
    case "SOPs":
    case "SOPsCompleted":
      return canView("standardOperatingProcedures");
    case "safetyMeetingReports":
      return canView("healthSafetyMeetings");
    case "dangerousGoods":
      return canView("dangerousGoodsRegister");
    case "equipmentManualDocuments":
      return canView("equipmentManualDocuments");
    case "customForms":
      return canView("customForms");
    case "customFormsCompleted":
      return (
        canView("customForms") ||
        (canView("crewParticulars") &&
          action.personnelIds &&
          action.personnelIds.length > 0) || // eslint-disable-line
        (action.personnelIds &&
          action.personnelIds.length > 0 &&
          action.personnelIds.includes(userId))
      );
    case "userDocuments":
      return (
        canView("crewParticulars") ||
        (action.personnelIds &&
          action.personnelIds.length > 0 &&
          action.personnelIds.includes(userId))
      );
    case "users":
    case "userPermissions":
      return canView("crewParticulars");
    case "userPermissionDefaults":
      return canCreate("crewParticulars");
    case "crewCertificates":
      return canView("crewCertificates");
    case "trainingTasks":
    case "trainingTaskReports":
      return canView("crewTraining");
    case "trainingTaskReportDocuments":
      return canView("crewTraining");
    case "contacts":
      return canView("contacts");
    case "companyPlan":
      return canView("companyPlan");
    case "companyDocuments":
      return canView("companyDocuments");
  }
  return true;
};
