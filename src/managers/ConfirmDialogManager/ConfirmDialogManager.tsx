import React, { useCallback } from "react";
import { View, StyleSheet } from "react-native";
import {
  DeleteableDocument,
  SharedStateConfig,
  sharedState,
} from "@src/shared-state/shared-state";
import { FieldValue } from "@src/lib/firebase/services/firestore.service";
import { SeaModal } from "@src/components/_atoms/SeaModal/SeaModal";

import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";

//
// <PERSON><PERSON> asking the user to confirm a particular action
//

interface ExtendedDeleteableDocument extends DeleteableDocument {
  state: "deleted";
  touched: FieldValue;
}

export type ConfirmDialog = {
  show: boolean;
  header?: string;
  confirmText: string;
  cancelText: string;
  subHeader?: string;
  message?: string;
  onConfirmed: () => void;
  onCancelled: () => void;
};

export const confirmDialogConfig: SharedStateConfig<ConfirmDialog> = {
  isAlwaysActive: true,
  default: {
    show: false,
    confirmText: "Yes",
    cancelText: "Cancel",
    onConfirmed: () => undefined,
    onCancelled: () => undefined,
  },
  notes:
    "Allows components to request confirmation from the user for a particular action.",
};

export const confirmAction = (
  header = "Are you sure?",
  confirmText?: string,
  cancelText?: string,
  subHeader?: string,
  message?: string,
): Promise<any> => {
  return new Promise((resolve, reject) => {
    sharedState.confirmDialog.set({
      show: true,
      header,
      confirmText: confirmText ?? "Yes",
      cancelText: cancelText ?? "Cancel",
      subHeader,
      message,
      onConfirmed: () => {
        console.log("confirmed!");
        resolve("confirmed");
      },
      onCancelled: () => {
        console.log("cancelled!");
        reject();
      },
    });
  });
};

// export const deleteIfConfirmed = (
//   collectionName: VesselDataSyncCollection | LicenseeDataSyncCollection,
//   itemId: string,
//   onDeleted: (batch: WriteBatch | SplittableBatch) => void,
//   itemType = "item",
//   itemDetail = "",
//   vesselIds: string[] | undefined = undefined,
//   personnelIds: string[] | undefined = undefined,
//   confirmHeader = undefined,
//   confirmText = undefined,
//   confirmCancel = "Cancel",
// ): Promise<any> => {
//   return confirmAction(
//     confirmHeader
//       ? confirmHeader
//       : `Are you sure you want to delete this ${itemType}?`,
//     confirmText ? confirmText : `Yes, delete`,
//     confirmCancel,
//     undefined,
//     undefined,
//   )
//     .then(() => {
//       const batch = splittableBatch(firestore, 20 - 0);
//       const batchTrace = makeBatchTrace(
//         batch,
//         collectionName,
//         "delete",
//         itemId,
//       );
//       const data = {
//         state: "deleted",
//         whenDeleted: Date.now(),
//         deletedBy: sharedState.userId.current,
//       } as ExtendedDeleteableDocument;
//       if (
//         vesselCollectionsToDataSync.includes(
//           collectionName as VesselDataSyncCollection,
//         ) ||
//         licenseeCollectionsToDataSync.includes(
//           collectionName as LicenseeDataSyncCollection,
//         )
//       ) {
//         data.touched = serverTimestamp();
//       }
//       batch.set(doc(firestore, collectionName, itemId), data, { merge: true });
//
//       logAction(
//         batch,
//         "Delete",
//         collectionName,
//         itemId,
//         itemDetail,
//         vesselIds,
//         personnelIds,
//       );
//
//       if (onDeleted) {
//         onDeleted(batch);
//       }
//
//       batchTrace.data = {
//         itemDetail,
//       };
//       batchTrace.save(`Delete confirmed ${collectionName}.${itemId}`);
//       batch
//         .commit()
//         .then(() => {
//           batchTrace.reportSuccess();
//         })
//         .catch((error) => {
//           batchTrace.reportError(error.message, error);
//         });
//
//       showToast(
//         `${itemType[0].toUpperCase()}${itemType.substring(1)} has been deleted`,
//       );
//     })
//     .catch((error) => {
//       console.log(`Delete cancelled for ${collectionName}.${itemId}`);
//       // Delete was cancelled, therefore do nothing
//     });
// };

const ConfirmDialogManager: React.FC = () => {
  const confirmDialog = sharedState.confirmDialog.use()!;

  const onCloseConfirm = useCallback(
    (confirmed: boolean) => {
      sharedState.confirmDialog.set(
        (current) =>
          ({
            ...current,
            show: false,
          }) as ConfirmDialog,
      );

      if (confirmed) {
        confirmDialog.onConfirmed();
      } else {
        confirmDialog.onCancelled();
      }
    },
    [confirmDialog],
  );

  if (!confirmDialog.show) return null;

  return (
    <SeaModal
      visible={confirmDialog.show}
      title={confirmDialog.header}
      onClose={() => onCloseConfirm(false)}
      style={{
        width: 400,
        maxWidth: "90%",
      }}
    >
      <View style={styles.dialog}>
        <View style={styles.buttonContainer}>
          <SeaButton
            key={"confirm-false"}
            variant={SeaButtonVariant.Secondary}
            label={confirmDialog.cancelText}
            onPress={() => onCloseConfirm(false)}
            viewStyle={{ minWidth: 100 }}
          />
          <SeaButton
            key={"confirm-true"}
            variant={SeaButtonVariant.Primary}
            label={confirmDialog.confirmText}
            onPress={() => onCloseConfirm(true)}
            viewStyle={{ minWidth: 100 }}
          />
        </View>
      </View>
    </SeaModal>
  );
};

const styles = StyleSheet.create({
  dialog: {
    elevation: 5,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginTop: 20,
  },
});

export default ConfirmDialogManager;
