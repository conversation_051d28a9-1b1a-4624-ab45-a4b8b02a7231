import React, { useEffect, useMemo, useState } from "react";
import * as ExpoFileSystem from "expo-file-system";
import { View, Text, StyleSheet, Pressable, Platform } from "react-native";
import { cachedFiles, CachedFileType, filesDirectory } from "@src/shared-state/FileSyncSystem/cachedFiles";
import { sharedState } from "@src/shared-state/shared-state";

const CachedFilesTab: React.FC = () => {
  const [fileStats, setFileStats] = useState<any>();
  const cacheStats = useMemo(() => {
    const stats = {
      entries: 0,
      states: {} as any,
      collections: {} as any,
      stored: {} as any,
      zeroSizes: 0,
      filesDirectory: filesDirectory
    } as any;

    Object.values(cachedFiles).forEach((entry) => {
      stats.entries++;
      const state = entry[0];
      const collection = entry[3] as string;
      const files = entry[5];

      if (stats.states[state] === undefined) {
        stats.states[state] = 0;
      }
      stats.states[state]++;

      if (stats.collections[collection] === undefined) {
        stats.collections[collection] = 0;
      }
      stats.collections[collection]++;

      Object.keys(files).forEach((type) => {
        if (stats.stored[type] === undefined) {
          stats.stored[type] = 0;
        }
        if (files[type as CachedFileType] === 0) {
          stats.zeroSizes++;
        } else {
          stats.stored[type]++;
        }
      });

    });

    return stats;
  }, []);

  // Gather fileStats
  useEffect(() => {
    let isActive = true;
    const path = `${filesDirectory}/${sharedState.licenseeId.current}`;
    if (Platform.OS === "web") {
      setFileStats({
        [path]: "No file listing implemented for web"
      });
    } else {
      ExpoFileSystem.getInfoAsync(filesDirectory).then((info) => {
        if (isActive) {
          if (info.exists) {
            return ExpoFileSystem.readDirectoryAsync(path);
          }
        }
      }).then((info) => {
        setFileStats({
          [path]: info
        });
      });
    }

    return () => {
      isActive = false;
    };
  }, []);

  return (
    <Pressable>
      <View>
        <Text>
          {JSON.stringify(cacheStats, undefined, 4)}
        </Text>
        <Text>
          {JSON.stringify(fileStats, undefined, 4)}
        </Text>
      </View>
    </Pressable>
  );
};

const style = StyleSheet.create({
  // ...
});

export default CachedFilesTab;
