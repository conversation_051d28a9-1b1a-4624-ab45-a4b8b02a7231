import React, { useMemo, useState } from "react";
import { View, Text, StyleSheet, Pressable, useWindowDimensions } from "react-native";
import {
  SharedState,
  sharedState,
  sharedStateCategories,
  SharedStateType,
  useAllSharedStateForAnalysis,
} from "@src/shared-state/shared-state";
import { debugViewStyles } from "../DebugViewManager";
import { SeaDropdown } from "@src/components/_atoms/SeaDropdown/SeaDropdown";

const SharedStateTab: React.FC = () => {
  const state = useAllSharedStateForAnalysis();
  const [category, setCategory] = useState<string>("Core");
  const [valuesToShow, setValuesToShow] = useState<any>({});

  const { width } = useWindowDimensions();

  const categoryOptions = useMemo(() => {
    return ["All", ...sharedStateCategories].map((category) => {
      return {
        label: category,
        value: category
      }
    });
  }, []);

  const toggleItem = (sharedStateType: SharedStateType) => {
    setValuesToShow((current: any) => {
      const newValuesToShow = {...current};
      if (current[sharedStateType]) {
        delete newValuesToShow[sharedStateType];
      } else {
        newValuesToShow[sharedStateType] = true;
      }
      return newValuesToShow;
    });
  };

  return (
    <>
      <View style={debugViewStyles.tabs}>
        <SeaDropdown
          items={categoryOptions}
          onSelect={(value) => {
            setCategory(value);
          }}
          value={category}
          style={{ flex: 1, width: "100%", maxWidth: 500 }}
        />
      </View>
      <View style={{ minWidth: 1100, width: (width - 40) }}>
        <View style={style.row}>
          {columns.map((column) => {
            return (
              <View key={column.name} style={column.style}>
                <Text style={[column.textStyle as any, { fontSize: 11 }]}>{column.name}</Text>
              </View>
            );
          })}
        </View>
        {Object.keys(sharedState).sort().map((sharedStateType) => {
          const sharedStateItem = sharedState[sharedStateType as SharedStateType];
          if (category && category !== "All" && sharedStateItem.category !== category) {
            return null;
          }
          const currentValue = state[sharedStateType as SharedStateType];
          return (
            <React.Fragment key={sharedStateType}>
              <Pressable
                style={[style.row, style.card, { opacity: sharedStateItem.isActive ? 1 : 0.4 }]}
                onPress={() => toggleItem(sharedStateType as SharedStateType)}
              >
                {columns.map((column, index) => {
                  return (
                    <View key={column.name} style={column.style}>
                      <Text style={[{fontSize: 12}, column.textStyle as any]}>{index === 0 ? sharedStateType : column.value(sharedStateItem)}</Text>
                    </View>
                  );
                })}
              </Pressable>
              <Pressable
                onPress={() => toggleItem(sharedStateType as SharedStateType)}
              >
                {valuesToShow[sharedStateType] &&
                  <View>
                    <Text style={style.value}>
                      {formatSharedStateValue(currentValue)}
                    </Text>
                  </View>
                }
              </Pressable>
            </React.Fragment>
          );
        })}
      </View>
    </>
  );
};

const maxValueLength = 100000;
const formatSharedStateValue = (value: any) => {
  if (!value) return ''+value;
  const s = JSON.stringify(value, undefined, 4);
  if (s.length > maxValueLength) {
    return s.substring(0, maxValueLength)+`... (${ String(10000 * 100 / s.length).substring(0, 4) }% shown)`;
  };
  return s;
};

const columns = [
  {
    name: 'Shared Data',
    style: { flex: 1.25 },
    textStyle: { textAlign: "left", fontSize: 14 },
    value: (item: SharedState<any>) => '',
  },
  {
    name: 'Live Docs',
    style: { flexBasis: 60 },
    textStyle: {textAlign: "center"},
    value: (item: SharedState<any>) => item.countLiveDocs() ?? "-",
  },
  {
    name: 'Runs',
    style: { flexBasis: 60 },
    textStyle: { textAlign: "center" },
    value: (item: SharedState<any>) => item.countRuns,
  },
  {
    name: 'Changed',
    style: { flexBasis: 60 },
    textStyle: { textAlign: "center" },
    value: (item: SharedState<any>) => item.countChanged,
  },
  {
    name: 'Subscribers',
    style: { flexBasis: 60 },
    textStyle: { textAlign: "center" },
    value: (item: SharedState<any>) => item.subscribers,
  },
  {
    name: 'Priority',
    style: { flexBasis: 60 },
    textStyle: { textAlign: "center" },
    value: (item: SharedState<any>) => item.priority,
  },
  {
    name: 'Active?',
    style: { flexBasis: 60 },
    textStyle: { textAlign: "center" },
    value: (item: SharedState<any>) => item.isAlwaysActive ? "Always" : (item.isActive ? "Yes" : "No"),
  },
  {
    name: 'Depends On',
    style: { flex: 1 },
    textStyle: { textAlign: "left" },
    value: (item: SharedState<any>) => item.dependencies.join(', '),
  },
  {
    name: 'Triggers',
    style: { flex: 1.5 },
    textStyle: { textAlign: "left" },
    value: (item: SharedState<any>) => item.triggers.join(', '),
  },
  {
    name: 'Notes',
    style: { flex: 1 },
    textStyle: { textAlign: "left" },
    value: (item: SharedState<any>) => item.notes,
  },
];

const style = StyleSheet.create({
  row: {
    flexDirection: "row",
    columnGap: 8,
    padding: 8,
  },
  card: {
    backgroundColor: "#ffffff",
    borderRadius: 10,
    marginBottom: 4,
  },
  value: {
    paddingLeft: 8,
    paddingBottom: 16,
    paddingRight: 8,
    fontSize: 12
  }
  // headerText: {
  //   color: colors.text.primary,
  //   fontFamily: fontFamily.BODY_FONT,
  //   fontWeight: "700",
  //   fontSize: 14,
  //   lineHeight: 22,
  //   letterSpacing: 0.5,
  // }
});


// const columnsStyles = StyleSheet.create(
  
// );

// const columnStyles = columns.map((column) => {
//   return StyleSheet.create({
//     ...column.style
//   });
// });


export default SharedStateTab;
