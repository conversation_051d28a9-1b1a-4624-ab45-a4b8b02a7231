import React, { useState } from "react";
import { View, Text, StyleSheet } from "react-native";
import {
  runSharedState,
  SharedState,
  sharedState,
} from "@src/shared-state/shared-state";
import { cachedDataInfo, profileDataSync, updateLicenseeCachedDataInfo, updateVesselCachedDataInfo } from "@src/shared-state/DataSyncSystem/cachedDataInfo";
import { LicenseeDataSyncCollection, VesselDataSyncCollection } from "@src/shared-state/DataSyncSystem/dataSyncTasks";

const DebugDataSyncTab: React.FC = () => {
  const dataSyncStatus = sharedState.dataSyncStatus.use();
  const [drawCount, setDrawCount] = useState(0); // eslint-disable-line @typescript-eslint/no-unused-vars

  const refreshLicenseeCache = (collection: string) => {
    updateLicenseeCachedDataInfo(collection as LicenseeDataSyncCollection, undefined);
    runSharedState('whenLicenseeTouched');
    setDrawCount((n) => n+1);
  };
  const refreshVesselCache = (vesselId: string, collection: string) => {
    updateVesselCachedDataInfo(vesselId, collection as VesselDataSyncCollection, undefined);
    runSharedState('whenVesselTouched');
    setDrawCount((n) => n+1);
  };

  return (
    <>
      <View>
        <Text style={style.group}>Cached Licensee Data</Text>
      </View>
      <View style={style.row}>
        {columns.map((column) => {
          return (
            <View key={column.name} style={column.style}>
              <Text style={[column.textStyle as any, { fontSize: 11 }]}>{column.name}</Text>
            </View>
          );
        })}
      </View>
      <View><Text>Under construction...</Text></View>
      {Object.keys(cachedDataInfo.licensee).sort().map((key) => {
        const when = cachedDataInfo.licensee?.[key as keyof typeof cachedDataInfo.licensee];
        return null; // Tmp: disabled
        return columns.map((column) => {
          if (!column.include()) {
            return null;
          }
          return (
            <View key={column.name} style={column.style}>
              <Text style={column.textStyle as any}>{column.name}</Text>
            </View>
          );
        });
        // return (
        //   <div key={key} className="card">
        //     <div style={{ flex: '0 250px' }}>{key}</div>
        //     <div style={{ flex: '0 130px' }}>{when ? formatDatetime(when, ' - ') : '-'}</div>
        //     {profileDataSync &&
        //       <>
        //         <div style={{ flex: '0 60px', textAlign: 'right' }}>{dataSyncProfiling.licensee[key as LicenseeDataSyncCollection]?.getType ?? '-'}</div>
        //         <div style={{ flex: '0 55px', textAlign: 'right' }}>{dataSyncProfiling.licensee[key as LicenseeDataSyncCollection]?.docs ?? '-'}</div>
        //         <div style={{ flex: '0 55px', textAlign: 'right' }}>{dataSyncProfiling.licensee[key as LicenseeDataSyncCollection]?.timeTaken ?? '-'}</div>
        //       </>
        //     }
        //     <div style={{ flex: '1 1 0', textAlign: 'right', color: 'var(--ion-color-primary)' }}>
        //       <div
        //         className="pushy"
        //         style={{ width: '20px', display: 'inline-block' }}
        //         onClick={(e) => refreshLicenseeCache(key)}
        //       >
        //         <SeaIcon icon="refresh" forceFontSize="18px" marginTop={-4} marginBottom={-5}/>
        //       </div>
        //     </div>
        //   </div>
        // );
      })}
    </>
  );
};

const columns = [
  {
    name: 'Collection',
    style: { flexBasis: 250 },
    textStyle: { textAlign: "left", fontSize: 14 },
    include: () => true,
    value: (item: SharedState<any>) => "hi",
  },
  {
    name: 'Last Touched',
    style: { flexBasis: 130 },
    textStyle: {textAlign: "center"},
    include: () => true,
    value: (item: SharedState<any>) => "hi",
  },
  {
    name: 'Last Op.',
    style: { flexBasis: 60 },
    textStyle: { textAlign: "right" },
    include: () => profileDataSync,
    value: (item: SharedState<any>) => "hi",
  },
  {
    name: 'Docs',
    style: { flexBasis: 60 },
    textStyle: { textAlign: "right" },
    include: () => profileDataSync,
    value: (item: SharedState<any>) => "hi",
  },
  {
    name: 'Time (ms)',
    style: { flexBasis: 60 },
    textStyle: { textAlign: "right" },
    include: () => profileDataSync,
    value: (item: SharedState<any>) => "hi",
  },
  {
    name: '', // Actions
    style: { flexBasis: 100 },
    textStyle: { textAlign: "center" },
    include: () => true,
    value: (item: SharedState<any>) => "hi",
  },
];

const style = StyleSheet.create({
  group: {
    fontSize: 16
  },
  row: {
    flexDirection: "row",
    columnGap: 8,
    padding: 8,
  },
  // card: {
  //   backgroundColor: "#ffffff",
  //   borderRadius: 10,
  //   marginBottom: 4,
  // },
  // value: {
  //   paddingLeft: 8,
  //   paddingBottom: 16,
  //   paddingRight: 8,
  //   fontSize: 12
  // }
});



export default DebugDataSyncTab;
