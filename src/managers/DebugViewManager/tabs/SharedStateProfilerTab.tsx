import React, { useMemo, useState } from "react";
import { View, Text, StyleSheet, Pressable, useWindowDimensions } from "react-native";
import {
  loadingSystem,
  sharedState,
  sharedStateProfiling,
  SharedStateType,
} from "@src/shared-state/shared-state";
import { SeaSelector } from "@src/components/_atoms/SeaSelector/SeaSelector";
import { debugViewStyles } from "../DebugViewManager";
import { colors } from "@src/theme/colors";

const SharedStateProfilerTab: React.FC = () => {
  const [refresh, setRefresh] = useState(1);
  const [sortByField, setSortByField] = useState<string>("runTime");
  const [valuesToShow, setValuesToShow] = useState<any>({});

  const { width } = useWindowDimensions();
  const tableWidth = Math.min(1000, Math.max(600, width - 40));

  const items = useMemo(() => {
    if (!refresh) return undefined;
    const list = Object.keys(sharedStateProfiling).map((sharedStateType) => {
      return {
        type: sharedStateType,
        runCount: sharedState[sharedStateType as SharedStateType].countRuns,
        changeCount: sharedState[sharedStateType as SharedStateType].countChanged,
        docCount: sharedState[sharedStateType as SharedStateType].countLiveDocs(),
        ...sharedStateProfiling[sharedStateType as SharedStateType]
      };
    });
    if (sortByField === 'type') {
      list.sort((a, b) => {
        return a.type.localeCompare(b.type);
      });
    } else {
      list.sort((a, b) => {
        return ((b as any)[sortByField] ?? 0) - ((a as any)[sortByField] ?? 0);
      });
    }
    return list;
  }, [refresh, sortByField]);

  const toggleItem = (sharedStateType: SharedStateType) => {
    setValuesToShow((current: any) => {
      const newValuesToShow = {...current};
      if (current[sharedStateType]) {
        delete newValuesToShow[sharedStateType];
      } else {
        newValuesToShow[sharedStateType] = true;
      }
      return newValuesToShow;
    });
  };

  return (
    <>
      <View style={debugViewStyles.tabs}>
        <SeaSelector
          items={
            sortByFields.map((field) => {
              return {
                label: field,
                value: field,
                onPress: () => setSortByField(field)
              };
            })
          }
          selectedValue={sortByField}
        />
      </View>
      <View style={{ flexDirection: "row", padding: 8, paddingTop: 0, paddingBottom: 4 }}>
        <View style={{ paddingTop: 3 }}>
          <Text>
            Loading: {JSON.stringify(loadingSystem.currentlyLoading)},
            Queue: {JSON.stringify(loadingSystem.queue)},
            Priority: {loadingSystem.loadingPriority}
          </Text>
        </View>
        <View style={{ paddingLeft: 32 }}>
          <Pressable onPress={() => {
            // Clear sharedStateProfiling
            for (const property in sharedStateProfiling) {
              delete (sharedStateProfiling as any)[property];
            }
            Object.values(sharedState).forEach((state) => {
              state.countRuns = 0;
              state.countChanged = 0;
            });
            setRefresh((current) => current + 1);
          }}>
            <Text style={{ color: colors.primary }}>Clear</Text>
          </Pressable>
        </View>
        <View style={{ paddingLeft: 16 }}>
          <Pressable onPress={() => setRefresh((current) => current + 1)}>
            <Text style={{ color: colors.primary }}>Refresh ({refresh})</Text>
          </Pressable>
        </View>
      </View>

      <View style={{ width: tableWidth }}>
        <View style={style.row}>
          {columns.map((column, index) => {
            return (
              <View key={column.name} style={column.style}>
                <Text style={[column.textStyle as any, { fontSize: 11 }]}>{column.name}</Text>
              </View>
            );
          })}
        </View>
        {items?.map((item) => {
          return (
            <React.Fragment key={item.type}>
              <Pressable
                style={[style.row, style.card]}
                onPress={() => toggleItem(item.type as SharedStateType)}
              >
                {columns.map((column, index) => {
                  return (
                    <View key={column.name} style={column.style}>
                      <Text style={[{fontSize: 12}, column.textStyle as any]}>{column.value(item)}</Text>
                    </View>
                  );
                })}
              </Pressable>
              {valuesToShow[item.type] &&
                <View>
                  <Text style={style.value}>
                    {renderProfile(item, tableWidth)}
                  </Text>
                </View>
              }
            </React.Fragment>
          );
        })}
      </View>

    </>
  );
};

const sortByFields = ["type", "runCount", "changeCount", "docCount", "runTime", "loadTime"];

const columns = [
  {
    name: 'Shared Data',
    style: { flex: 275 },
    textStyle: { textAlign: "left", fontSize: 14},
    value: (item: any) => item.type,
  },
  {
    name: 'Runs',
    style: { flex: 80 },
    textStyle: {textAlign: "right" },
    value: (item: any) => item.runCount,
  },
  {
    name: 'Changed',
    style: { flex: 80 },
    textStyle: { textAlign: "right" },
    value: (item: any) => item.changeCount,
  },
  {
    name: 'Live Docs',
    style: { flex: 80 },
    textStyle: { textAlign: "right" },
    value: (item: any) => item.docCount,
  },
  {
    name: 'Run Time (ms)',
    style: { flex: 180 },
    textStyle: { textAlign: "right" },
    value: (item: any) => renderTimeTaken(item.runTime ?? 0),
  },
  {
    name: 'Load Time (ms)',
    style: { flex: 180 },
    textStyle: { textAlign: "right" },
    value: (item: any) => renderTimeTaken(item.loadTime ?? 0),
  },
];

const renderTimeTaken = (millisFloat: number) => {
  return millisFloat.toFixed(1);
};

const blue = "#97bdff";

const renderProfile = (item: any, tableWidth: number) => {

  const barWidth = (tableWidth - 80 - 100 - 100 - 100 - 20) / 2;

  const whens = Object.keys(item.runs);
  if (whens.length === 0) {
    return <Text>No data</Text>;
  }
  whens.sort((a, b) => {
    return Number(a) - Number(b);
  });
  let maxRunTime = 0;
  let maxLoadTime = 0;
  whens.forEach((when) => {
    if (item.runs[when] && item.runs[when] > maxRunTime) {
      maxRunTime = item.runs[when];
    }
    if (item.loads[when] && item.loads[when] > maxLoadTime) {
      maxLoadTime = item.loads[when];
    }
  });
  let previousWhen = Number(whens[0]);
  return (
    <View>
      <View style={{ flexDirection: "row" }}>
        <View style={{ flexBasis: 80 }}></View>
        <View style={{ flexBasis: 100 }}><Text style={{ textAlign: "right" }}>When</Text></View>
        <View style={{ flexBasis: 100 }}><Text style={{ textAlign: "right" }}>Run Time</Text></View>
        <View style={{ flexBasis: barWidth }}></View>
        <View style={{ flexBasis: 100 }}><Text style={{ textAlign: "right" }}>Load Time</Text></View>
        <View style={{ flexBasis: barWidth }}></View>
      </View>
      {whens.map((when, index) => {
        const diff = Number(when) - previousWhen;
        previousWhen = Number(when);
        return (
          <View key={when} style={{ flexDirection: "row" }}>
            <View style={{ flexBasis: 80 }}>
              <Text style={{ textAlign: "right" }}>{index > 0 && `+${renderTimeTaken(Number(diff))}`}</Text>
            </View>
            <View style={{ flexBasis: 100 }}><Text style={{ textAlign: "right" }}>{renderTimeTaken(Number(when))}</Text></View>
            <View style={{ flexBasis: 100 }}><Text style={{ textAlign: "right" }}>{renderTimeTaken(Number(item.runs[when]))}</Text></View>
            <View style={{ flexBasis: barWidth }}>
              {maxRunTime &&
                <View style={{ marginTop: 4, marginLeft: 4, height: 10, width: (item.runs[when] ?? 0) * barWidth / maxRunTime, backgroundColor: blue }}></View>
              }
            </View>
            <View style={{ flexBasis: 100 }}><Text style={{ textAlign: "right" }}>{renderTimeTaken(Number(item.loads[when]))}</Text></View>
            <View style={{ flexBasis: barWidth }}>
              {maxLoadTime &&
                <View style={{ marginTop: 4, marginLeft: 4, height: 10, width: (item.loads[when] ?? 0) * barWidth / maxLoadTime, backgroundColor: blue }}></View>
              }
            </View>
          </View>
        );
      })}
    </View>
  );
};

const style = StyleSheet.create({
  row: {
    flexDirection: "row",
    columnGap: 8,
    padding: 8,
    maxWidth: 1000,
  },
  card: {
    backgroundColor: "#ffffff",
    borderRadius: 10,
    marginBottom: 4,
  },
  value: {
    paddingLeft: 8,
    paddingBottom: 16,
    paddingRight: 8,
    fontSize: 12,
  }
});

export default SharedStateProfilerTab;
