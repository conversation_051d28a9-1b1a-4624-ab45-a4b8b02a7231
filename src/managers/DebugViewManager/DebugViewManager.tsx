import React from "react";
import { View, Text, StyleSheet, ScrollView, Pressable } from "react-native";
import {
  sharedState,
} from "@src/shared-state/shared-state";
import { SeaSelector } from "@src/components/_atoms/SeaSelector/SeaSelector";
import { activateDebugger, DebugViewConfig } from "@src/shared-state/General/debugView";
import SharedStateTab from "./tabs/SharedStateTab";
import SharedStateProfilerTab from "./tabs/SharedStateProfilerTab";
import DebugDataSyncTab from "./tabs/DebugDataSyncTab";
import CachedFilesTab from "./tabs/CachedFilesTab";

const DebugViewManager: React.FC = () => {
  const debugView = sharedState.debugView.use()!;

  const selectTab = (tab: string) => {
    sharedState.debugView.set((current) => {
      return {
        ...current,
        tab
      } as DebugViewConfig
    });
  }

  return (
    <>
      <View
        style={{
          display: debugView.show ? 'flex' : 'none',
          ...styles.overlay
        }}
      >
        <View style={{ padding: 12, paddingBottom: 0 }}>
          <View style={styles.tabs}>
            <SeaSelector
              items={
                tabs.map((tab) => {
                  return {
                    label: tab,
                    value: tab,
                    onPress: () => selectTab(tab)
                  };
                })
              }
              selectedValue={debugView.tab}
            />
          </View>
        </View>
        <ScrollView horizontal={true} showsHorizontalScrollIndicator={true}>
          <ScrollView showsVerticalScrollIndicator={true}>
            <View style={{ padding: 12 }}>
              {debugView.tab === 'State' && <SharedStateTab />}
              {debugView.tab === 'Profiler' && <SharedStateProfilerTab />}
              {debugView.tab === 'DataSync' && <DebugDataSyncTab />}
              {debugView.tab === 'Files' && <CachedFilesTab />}
            </View>
          </ScrollView>
        </ScrollView>
      </View>
      <Pressable style={[styles.quick, debugView.show ? {} : {boxShadow: "0 0px 12px rgba(0, 0, 0, 0.5)"}]} onPress={activateDebugger}>
        {debugView.show && <Text style={styles.x}>x</Text>}
      </Pressable>
    </>
  );
};

const tabs = [
  "State",
  "Profiler",
  "DataSync",
  "Files"
];

const panelBlue = "rgb(202,214,235)";

const styles = StyleSheet.create({
  quick: {
    position: "absolute",
    top: 0,
    right: 0,
    width: 40,
    height: 40,
    backgroundColor: panelBlue,
    borderBottomLeftRadius: 20,
  },
  x: {
    textAlign: "center",
    fontSize: 20,
    paddingTop: 8
  },
  overlay: {
    backgroundColor: panelBlue,
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0
  },
  tabs: {
    alignItems: "flex-start",
    paddingBottom: 12
  }
});

export const debugViewStyles = styles;

export default DebugViewManager;
