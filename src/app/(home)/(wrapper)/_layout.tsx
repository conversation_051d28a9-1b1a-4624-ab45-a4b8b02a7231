import React from "react";
import { Stack, useNavigation } from "expo-router";
import StandardPageLayout from "@src/layout/StandardPageLayout/StandardPageLayout";

export default function wrapperLayout() {
  const navigation = useNavigation();
  console.log("TT- wrapper navigation", navigation.getState());
  return (
    <StandardPageLayout>
      <Stack
        screenOptions={{
          gestureEnabled: false,
          headerBackVisible: false,
          animation: "none",
          headerShown: false,
        }}
      />
    </StandardPageLayout>
  );
}
