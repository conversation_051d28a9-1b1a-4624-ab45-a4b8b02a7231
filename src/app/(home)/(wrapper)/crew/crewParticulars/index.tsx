import React from "react";
import { Crew } from "@src/pages/Crew/Crew";
import { CrewParticularsList } from "@src/pages/Crew/CrewParticularsList";
import { sharedState } from "@src/shared-state/shared-state";
import { Routes } from "@src/navigation/constants";
import { useCrewSubNav } from "@src/hooks/useSubNav";

const CrewParticularsListPage = () => {
  const vesselId = sharedState.vesselId.use();
  return (
    <>
      <Crew visible={true}>
        <CrewParticularsList
          vesselId={vesselId}
          visible={true}
          headerSubNavigation={useCrewSubNav(
            vesselId,
            Routes.CREW_PARTICULARS_LIST,
          )}
        />
      </Crew>
    </>
  );
};

export default CrewParticularsListPage;
