import { usePathname, useRouter } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";

export type SubNavRoute = {
  title: string;
  path: string;
};

export const useSubNav = (
  vesselId: string | undefined,
  routes: SubNavRoute[],
  activeRoute?: Routes,
) => {
  const router = useRouter();

  const navigateToRoute = (path: string, vesselId?: string) => {
    router.navigate({
      pathname: getRoutePath(path),
      params: {
        vesselId: vesselId,
      },
    });
  };

  const isActive = (path: string) => activeRoute === path;

  return routes.map(({ title, path }) => ({
    title,
    onPress: () => navigateToRoute(path, vesselId),
    isActive: isActive(path),
  }));
};

export const useSafetySubNav = (vesselId?: string, activeRoute?: Routes) => {
  const subNavRoutes = [
    { title: "Safety Checks", path: Routes.SAFETY_EQUIPMENT_CHECKS },
    {
      title: "Safety Equipment Expiries",
      path: Routes.SAFETY_EQUIPMENT_EXPIRIES,
    },
    { title: "Drills", path: Routes.DRILLS },
  ];

  return useSubNav(vesselId, subNavRoutes, activeRoute);
};

export const useMaintenanceSubNav = (
  vesselId?: string,
  activeRoute?: Routes,
) => {
  const subNavRoutes = [
    { title: "Maintenance Schedule", path: Routes.MAINTENANCE_SCHEDULE },
    { title: "Job List", path: Routes.JOBLIST },
    { title: "Spare Parts List", path: Routes.SPARE_PARTS_LIST },
    { title: "Equipment List", path: Routes.EQUIPMENT_LIST },
    { title: "Equipment Manuals", path: Routes.EQUIPMENT_MANUALS },
    { title: "Maintenance History", path: Routes.MAINTENANCE_HISTORY },
  ];

  return useSubNav(vesselId, subNavRoutes, activeRoute);
};

export const useHealthAndSafetySubNav = (
  vesselId?: string,
  activeRoute?: Routes,
) => {
  const subNavRoutes = [
    { title: "Incident / Event Reports", path: Routes.INCIDENT_REPORT },
    { title: "Risk Assessments", path: Routes.RISK_ASSESSMENT },
    { title: "Health & Safety Meetings", path: Routes.HEALTH_SAFETY_MEETING },
    {
      title: "Dangerous Goods Register",
      path: Routes.DANGEROUS_GOODS_REGISTER,
    },
  ];

  return useSubNav(vesselId, subNavRoutes, activeRoute);
};

export const useCrewSubNav = (vesselId?: string, activeRoute?: Routes) => {
  const subNavRoutes = [
    { title: "Crew Particulars", path: Routes.CREW_PARTICULARS_LIST },
    { title: "Crew Certificates", path: Routes.CREW_CERTIFICATES },
  ];

  return useSubNav(vesselId, subNavRoutes, activeRoute);
};

export const useCrewParticularsSubNav = (
  vesselId?: string,
  crewId?: string,
  activeRoute?: Routes,
) => {
  const subNavRoutes = [
    { title: "Profile", path: Routes.CREW_PARTICULARS_VIEW_PROFILE },
    { title: "Forms / Documents", path: Routes.CREW_PARTICULARS_VIEW_FORMS },
    { title: "Sea Time", path: Routes.CREW_PARTICULARS_VIEW_SEATIME },
    { title: "Certificates", path: Routes.CREW_PARTICULARS_VIEW_CERTIFICATES },
    { title: "Drills", path: Routes.CREW_PARTICULARS_VIEW_DRILLS },
    { title: "Training", path: Routes.CREW_PARTICULARS_VIEW_TRAINING },
  ];

  const router = useRouter();
  const navigateToRoute = (path: string) => {
    router.navigate({
      pathname: getRoutePath(path),
      params: {
        vesselId: vesselId,
        crewId: crewId,
        tab: path,
      },
    });
  };

  return subNavRoutes.map(({ title, path }) => ({
    title,
    onPress: () => navigateToRoute(path),
    isActive: activeRoute === path,
  }));
};

export const useVesselDocumentSubNav = (
  vesselId?: string,
  activeRoute?: Routes,
) => {
  const subNavRoutes = [
    { title: "Vessel Certificates", path: Routes.VESSEL_CERTIFICATES },
    { title: "Vessel Documents", path: Routes.VESSEL_DOCUMENTS },
    { title: "Survey Documents", path: Routes.SURVEY_DOCUMENTS },
    {
      title: "Standard Operating Procedures",
      path: Routes.STANDARD_OPERATING_PROCEDURES,
    },
  ];

  return useSubNav(vesselId, subNavRoutes, activeRoute);
};
