import {
  RowProps,
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";

import { MaintenanceFilters } from "@src/components/_organisms/Maintenance/MaintenanceSchedule/MaintenanceFilters";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { CategoriesData, renderCategoryName } from "@src/lib/categories";
import {
  formatDateShort,
  formatMonthISO,
  formatMonthLonger,
} from "@src/lib/datesAndTime";
import { formatValue, renderCamelCase, truncateText } from "@src/lib/util";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { renderFullNameForUserId } from "@src/shared-state/Core/users";
import { sharedState } from "@src/shared-state/shared-state";
import { MaintenanceTaskCompleted } from "@src/shared-state/VesselMaintenance/maintenanceTasksCompleted";
import React, { useCallback, useMemo, useState } from "react";
import {
  FlatList,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { SeaSettingsButton } from "@src/components/_molecules/IconButtons/SeaSettingsButton";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import {
  SeaPageCard,
  SubNav,
} from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { usePermission } from "@src/hooks/usePermission";
import { useRouter } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { EditMaintenanceHistoryDrawer } from "@src/components/_organisms/Maintenance/MaintenanceHistory/EditMaintenanceHistoryDrawer";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { ScrollablePageLayout } from "@src/layout/ScrollablePageLayout/ScrollablePageLayout";

const MaintenanceHistoryPermissions = {
  equipmentManualDocuments: { level: permissionLevels.CREATE },
};

interface MaintenanceHistoryProps {
  headerSubNavigation?: SubNav[];
}

export const MaintenanceHistory = ({
  headerSubNavigation,
}: MaintenanceHistoryProps) => {
  const vessel = sharedState.vessel.use();
  const vesselSystems = sharedState.vesselSystems.use();
  const maintenanceTasksCompleted = sharedState.maintenanceTasksCompleted.use();
  const equipment = sharedState.equipment.use();

  const router = useRouter();

  const [systemFilter, setSystemFilter] = useState("");
  const [equipmentFilter, setEquipmentFilter] = useState("");
  const [completedByFilter, setCompletedByFilter] = useState("");
  const [isVisibleAddNewDrawer, setIsVisibleAddNewDrawer] = useState(false);

  const modulePermissions = usePermission<typeof MaintenanceHistoryPermissions>(
    {
      modules: MaintenanceHistoryPermissions,
    },
  );

  const handleRow = useCallback((item: MaintenanceTaskCompleted) => {
    router.navigate({
      pathname: getRoutePath(Routes.MAINTENANCE_HISTORY_VIEW),
      params: {
        vesselId: vessel?.id,
        taskId: item.id,
        name: item.equipment?.equipment,
      },
    });
    // setSelectedTask(item);
    // setViewTask(true);
  }, []);

  const systemFilterOptions = useMemo(() => {
    if (!maintenanceTasksCompleted?.filterOptions) return [];

    const options = maintenanceTasksCompleted.filterOptions.systemIds.map(
      (id: string) => ({
        label: renderCategoryName(id, vesselSystems),
        value: id,
      }),
    );

    return [
      {
        label: "All",
        value: "",
      },
      ...options,
    ];
  }, [maintenanceTasksCompleted]);

  const equipmentFilterOptions = useMemo(() => {
    if (!maintenanceTasksCompleted?.filterOptions) return [];

    const options = maintenanceTasksCompleted.filterOptions.equipmentIds.map(
      (id: string) => ({
        label: equipment?.byId[id].equipment ?? "",
        value: id,
      }),
    );

    const filteredOptions = options.filter((option) => {
      if (systemFilter) {
        const _equipment = equipment?.byId[option.value];
        return _equipment?.systemId === systemFilter;
      }
      return true;
    });

    return [
      {
        label: "All",
        value: "",
      },
      ...filteredOptions,
    ];
  }, [maintenanceTasksCompleted, systemFilter]);

  const completedByFilterOptions = useMemo(() => {
    if (!maintenanceTasksCompleted?.filterOptions) return [];

    const options = maintenanceTasksCompleted.filterOptions.completedBy.map(
      (id: string) => ({
        label: renderFullNameForUserId(id),
        value: id,
      }),
    );

    return [
      {
        label: "All",
        value: "",
      },
      ...options,
    ];
  }, [maintenanceTasksCompleted]);

  console.log("TT- count", maintenanceTasksCompleted?.all.length);

  const filteredHistory = useMemo(() => {
    if (!maintenanceTasksCompleted) return [];

    //TODO: Add pagination
    let filteredData = maintenanceTasksCompleted.all;

    if (systemFilter) {
      filteredData = filteredData.filter(
        (item) => item.equipment?.systemId === systemFilter,
      );
    }

    if (equipmentFilter) {
      filteredData = filteredData.filter(
        (item) => item.equipment?.id === equipmentFilter,
      );
    }

    if (completedByFilter) {
      filteredData = filteredData.filter(
        (item) => item.completedBy === completedByFilter,
      );
    }

    return filteredData.sort((a, b) => b.whenCompleted - a.whenCompleted);
  }, [
    maintenanceTasksCompleted,
    systemFilter,
    equipmentFilter,
    completedByFilter,
  ]);

  const columns = useMemo(() => buildColumns(vesselSystems), [vesselSystems]);
  const rows = useMemo(() => {
    return buildRows(filteredHistory, (item: MaintenanceTaskCompleted) =>
      handleRow(item),
    );
  }, [filteredHistory]);

  return (
    <ScrollablePageLayout>
      <RequirePermissions
        role="maintenanceHistory"
        level={permissionLevels.VIEW}
        showDenial={true}
      >
        <SeaPageCard
          title={"Maintenance History"}
          primaryActionButton={
            modulePermissions.equipmentManualDocuments ? (
              <SeaButton
                onPress={() => setIsVisibleAddNewDrawer(true)}
                variant={SeaButtonVariant.Primary}
                label={"Add New"}
                iconOptions={{ icon: "add" }}
              />
            ) : undefined
          }
          secondaryActionButton={[
            <SeaDownloadButton
              key={"download"}
              onPress={() => alert("This functionality is not completed yet")}
            />,
            <SeaSettingsButton
              key={"settings"}
              onPress={() => alert("This functionality is not completed yet")}
            />,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Filter Row */}
        <MaintenanceFilters
          systemFilter={{
            value: systemFilter,
            setValue: setSystemFilter,
            options: systemFilterOptions,
          }}
          equipmentFilter={{
            value: equipmentFilter,
            setValue: setEquipmentFilter,
            options: equipmentFilterOptions,
          }}
          completedByFilter={{
            value: completedByFilter,
            setValue: setCompletedByFilter,
            options: completedByFilterOptions,
          }}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable
            columns={columns}
            showGroupedTable={true}
            rows={rows}
            sortGroupsByTitle={sortGroupsByTitle}
          />
        </View>
        {isVisibleAddNewDrawer && (
          <EditMaintenanceHistoryDrawer
            visible={isVisibleAddNewDrawer}
            onClose={() => setIsVisibleAddNewDrawer(false)}
            type="new"
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  );
};

const buildColumns = (vesselSystems?: CategoriesData) => {
  return [
    {
      label: "Date",
      value: (item) => formatDateShort(item.whenCompleted),
      style: { fontWeight: "bold" },
    },
    {
      label: "Engine Hours",
      value: (item) => item.engineHours,
    },
    {
      label: "System",
      value: (item) =>
        formatValue(
          renderCategoryName(item.equipment?.systemId, vesselSystems),
        ),
    },
    {
      label: "Equipment",
      value: (item) =>
        `${formatValue(item.equipment?.equipment)} ${item.equipment?.state === "deleted" ? "(deleted)" : ""}`,
    },
    {
      label: "Task",
      value: (item) => truncateText(formatValue(item.task), 50),
    },
    {
      label: "Type",
      value: (item) =>
        formatValue(
          item.type === "unscheduled" ? "Job" : renderCamelCase(item.type),
        ),
    },
    {
      label: "Completed By",
      value: (item) => renderFullNameForUserId(item.completedBy),
    },
  ] as SeaTableColumn<MaintenanceTaskCompleted>[];
};

const buildRows = (
  items: MaintenanceTaskCompleted[],
  onPress: (item: MaintenanceTaskCompleted) => void,
) => {
  return items.map((item) => ({
    data: item,
    onPress: (item: MaintenanceTaskCompleted) => onPress(item),
    group: (item: MaintenanceTaskCompleted) =>
      formatMonthLonger(formatMonthISO(item.whenCompleted)),
  }));
};

const sortGroupsByTitle = (a: string, b: string) => {
  return new Date(b).getTime() - new Date(a).getTime();
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  filterRow: {
    // backgroundColor: "magenta",
  },
  title: {
    // backgroundColor: "magenta"
  },
  actions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  tableView: {
    // backgroundColor: "red",
    marginTop: 16,
  },
});
