import SeaFileImage from "@src/components/_atoms/SeaFileImage/SeaFileImage";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";

import { MaintenanceFilters } from "@src/components/_organisms/Maintenance/MaintenanceSchedule/MaintenanceFilters";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { CategoriesData, renderCategoryName } from "@src/lib/categories";
import { truncateText } from "@src/lib/util";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { sharedState } from "@src/shared-state/shared-state";
import { EquipmentManualDocument } from "@src/shared-state/VesselDocuments/equipmentManualDocuments";
import { EquipmentData } from "@src/shared-state/VesselMaintenance/equipment";
import React, { useCallback, useMemo, useState } from "react";
import { FlatList, ScrollView, StyleSheet, View } from "react-native";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import { SeaSettingsButton } from "@src/components/_molecules/IconButtons/SeaSettingsButton";
import {
  SeaPageCard,
  SubNav,
} from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { usePermission } from "@src/hooks/usePermission";
import { useRouter } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { EditEquipmentManualDrawer } from "@src/components/_organisms/Maintenance/EquipmentManual/EditEquipmentManualDrawer";
import { ScrollablePageLayout } from "@src/layout/ScrollablePageLayout/ScrollablePageLayout";

const EquipmentManualsPermissions = {
  equipmentManualDocuments: { level: permissionLevels.CREATE },
};

interface EquipmentManualProps {
  headerSubNavigation?: SubNav[];
}

export const EquipmentManual = ({
  headerSubNavigation,
}: EquipmentManualProps) => {
  const vesselId = sharedState.vesselId.use();
  const equipment = sharedState.equipment.use();
  const equipmentManualDocuments = sharedState.equipmentManualDocuments.use();
  const vesselSystems = sharedState.vesselSystems.use();

  const [systemFilter, setSystemFilter] = useState("");
  const [equipmentFilter, setEquipmentFilter] = useState("");
  const [isVisibleAddNewDrawer, setIsVisibleAddNewDrawer] = useState(false);

  const router = useRouter();
  const modulePermissions = usePermission<typeof EquipmentManualsPermissions>({
    modules: EquipmentManualsPermissions,
  });

  const systemFilterOptions = useMemo(() => {
    if (!equipmentManualDocuments?.filterOptions) return [];

    const options = equipmentManualDocuments.filterOptions.systemIds.map(
      (id: string) => ({
        label: renderCategoryName(id, vesselSystems),
        value: id,
      }),
    );

    return [
      {
        label: "All",
        value: "",
      },
      ...options,
    ];
  }, [equipmentManualDocuments]);

  const equipmentFilterOptions = useMemo(() => {
    if (!equipmentManualDocuments?.filterOptions) return [];

    const options = equipmentManualDocuments.filterOptions.equipmentIds.map(
      (id: string) => ({
        label: equipment?.byId[id].equipment ?? "",
        value: id,
      }),
    );

    const filteredOptions = options.filter((option) => {
      if (systemFilter) {
        const _equipment = equipment?.byId[option.value];
        return _equipment?.systemId === systemFilter;
      }
      return true;
    });

    return [
      {
        label: "All",
        value: "",
      },
      ...filteredOptions,
    ];
  }, [equipmentManualDocuments, systemFilter]);

  const filteredEquipmentManual = useMemo(() => {
    if (!equipmentManualDocuments) return undefined;

    let filteredManual: EquipmentManualDocument[] =
      equipmentManualDocuments.documents;

    // Filter by system
    if (systemFilter) {
      filteredManual = filteredManual.filter((manual) =>
        manual?.systemIds?.includes(systemFilter),
      );
    }

    // //Filter by location
    if (equipmentFilter) {
      filteredManual = filteredManual.filter((manual) =>
        manual.equipmentIds?.includes(equipmentFilter),
      );
    }

    return filteredManual;
  }, [equipmentManualDocuments, systemFilter, equipmentFilter]);

  const handleRow = useCallback((item: EquipmentManualDocument) => {
    router.navigate({
      pathname: getRoutePath(Routes.EQUIPMENT_MANUALS_VIEW),
      params: {
        vesselId: vesselId,
        manualId: item.id,
      },
    });
  }, []);

  const rows = useMemo(
    () =>
      buildRows(
        filteredEquipmentManual ?? [],
        (item: EquipmentManualDocument) => handleRow(item),
      ),
    [filteredEquipmentManual, handleRow],
  );
  const columns = useMemo(
    () => buildColumns(vesselSystems, equipment),
    [vesselSystems, equipment],
  );
  return (
    <ScrollablePageLayout>
      <RequirePermissions
        role="equipmentManualDocuments"
        level={permissionLevels.VIEW}
        showDenial={true}
      >
        <SeaPageCard
          title={"Equipment Manuals"}
          primaryActionButton={
            modulePermissions.equipmentManualDocuments ? (
              <SeaButton
                onPress={() => setIsVisibleAddNewDrawer(true)}
                variant={SeaButtonVariant.Primary}
                label={"Add New"}
                iconOptions={{ icon: "add" }}
              />
            ) : undefined
          }
          secondaryActionButton={[
            <SeaDownloadButton
              key={"download"}
              onPress={() => alert("This functionality is not completed yet")}
            />,
            <SeaSettingsButton
              key={"settings"}
              onPress={() => alert("This functionality is not completed yet")}
            />,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Filter Row */}
        <MaintenanceFilters
          systemFilter={{
            value: systemFilter,
            setValue: setSystemFilter,
            options: systemFilterOptions,
          }}
          equipmentFilter={{
            value: equipmentFilter,
            setValue: setEquipmentFilter,
            options: equipmentFilterOptions,
          }}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable columns={columns} rows={rows} />
        </View>
        {isVisibleAddNewDrawer && (
          <EditEquipmentManualDrawer
            visible={isVisibleAddNewDrawer}
            onClose={() => setIsVisibleAddNewDrawer(false)}
            type="new"
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  );
};

const buildColumns = (
  vesselSystems?: CategoriesData,
  equipment?: EquipmentData,
) => {
  return [
    {
      label: "",
      render: (item) => <SeaFileImage files={item.files} size={"tiny"} />,
      widthPercentage: 0.1,
    },
    {
      label: "Document Name",
      value: (item) => `${truncateText(item.title, 100)}`,
      style: { fontWeight: "bold" },
      widthPercentage: 0.4,
    },
    {
      label: "System",
      value: (item) => {
        return item.systemIds
          ? item.systemIds
              .map((id: string) => {
                return renderCategoryName(id, vesselSystems);
              })
              .join(", ")
          : "-";
      },
    },
    {
      label: "Equipment",
      value: (item) =>
        equipment?.byId && item.equipmentIds
          ? item.equipmentIds
              .map((id: string) => {
                return `${equipment.byId[id]?.equipment}${equipment.byId[id]?.state === "deleted" ? " (deleted)" : ""}`;
              })
              .join(", ")
          : "-",
    },
  ] as SeaTableColumn<EquipmentManualDocument>[];
};

const buildRows = (
  items: EquipmentManualDocument[],
  onPress: (item: EquipmentManualDocument) => void,
) => {
  return items.map((item) => ({
    data: item,
    onPress: (item: EquipmentManualDocument) => onPress(item),
  }));
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  filterRow: {
    // backgroundColor: "magenta",
  },
  title: {
    // backgroundColor: "magenta"
  },
  actions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  tableView: {
    marginTop: 16,
  },
});
