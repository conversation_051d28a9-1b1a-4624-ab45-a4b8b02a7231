import { sharedState } from "@src/shared-state/shared-state";
import {
  SeaPageCard,
  SubNav,
} from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import React, { useMemo, useState } from "react";
import { DrillsTable } from "@src/components/_organisms/Safety/Drills/DrillsTable";
import { SeaLoadingSpinner } from "@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner";
import { useDrillsMatrix } from "@src/hooks/useDrillsMatrix";
import { SeaAddButton } from "@src/components/_molecules/IconButtons/SeaAddButton";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { useRouter } from "expo-router";
import { SeaHistoryButton } from "@src/components/_molecules/IconButtons/SeaHistoryButton";
import { DrillHistoryDrawer } from "@src/components/_organisms/Safety/Drills/DrillHistoryDrawer";
import { ModifyDrillReportDrawer } from "@src/components/_organisms/Safety/Drills/ModifyDrillReportDrawer";
import { DrawerMode } from "@src/components/_atoms/SeaDrawer/SeaDrawer";
import { useDeviceWidth } from "@src/hooks/useDevice";
import { SeaSpacer } from "@src/components/_atoms/SeaSpacer/SeaSpacer";
import { ScrollablePageLayout } from "@src/layout/ScrollablePageLayout/ScrollablePageLayout";

export type DrillsPageProps = {
  vesselId: string;
  visible: boolean;
  headerSubNavigation: SubNav[];
};

export const Drills = ({
  vesselId,
  visible,
  headerSubNavigation,
}: DrillsPageProps) => {
  const vesselDrillsData = sharedState.vesselDrills.use(visible);
  const drillReportData = sharedState.drillReports.use(visible);
  const licenseeUsers = sharedState.users.use(visible);

  const drillMatrixData = useDrillsMatrix(
    vesselId,
    vesselDrillsData?.all,
    licenseeUsers?.all,
  );

  const drillReports = useMemo(
    () => drillReportData?.allReports.filter((x) => x.state === "active"),
    [drillReportData],
  );

  const [isHistoryDrawerVisible, setIsHistoryDrawerVisible] = useState(false);
  const [
    isCreateDrillReportDrawerVisible,
    setIsCreateDrillReportDrawerVisible,
  ] = useState(false);

  const { isMobileWidth } = useDeviceWidth();

  const router = useRouter();

  const onUserPress = (userId?: string) => {
    return router.navigate({
      pathname: getRoutePath(Routes.DRILLS_VIEW_USER_DRILLS),
      params: {
        userId,
        vesselId,
      },
    });
  };

  const onDrillPress = (drillId?: string) => {
    return router.navigate({
      pathname: getRoutePath(Routes.DRILLS_VIEW_DRILL),
      params: {
        drillId,
        vesselId,
      },
    });
  };

  const onCellPress = (userId?: string, drillId?: string) => {
    return router.navigate({
      pathname: getRoutePath(Routes.DRILLS_VIEW_USER_DRILL),
      params: {
        userId,
        drillId,
        vesselId,
      },
    });
  };

  return (
    <ScrollablePageLayout>
      <SeaPageCard
        title="Drills"
        primaryActionButton={
          <SeaButton
            onPress={() => setIsCreateDrillReportDrawerVisible(true)}
            variant={SeaButtonVariant.Primary}
            label={"Complete"}
            iconOptions={{ icon: "check" }}
          />
        }
        secondaryActionButton={[
          <SeaDownloadButton
            key={"download"}
            onPress={() => alert("This functionality is not completed yet")}
          />,
          <SeaAddButton
            key={"add-drill"}
            onPress={() => alert("This functionality is not completed yet")}
          />,
          <SeaHistoryButton
            key={"drill-history"}
            label={!isMobileWidth ? "History" : ""}
            onPress={() => {
              return router.navigate({
                pathname: getRoutePath(Routes.DRILLS_VIEW_DRILLS_HISTORY),
                params: {
                  vesselId,
                },
              });
            }}
          />,
        ]}
        subNav={headerSubNavigation}
      />
      {drillMatrixData ? (
        <>
          <DrillsTable
            drillMatrixData={drillMatrixData}
            onRowPress={(userId) => onUserPress(userId)}
            onColumnPress={(drillId) => onDrillPress(drillId)}
            onCellPress={(userId, drillId) => onCellPress(userId, drillId)}
          />
          <SeaSpacer height={50} />
        </>
      ) : (
        <SeaLoadingSpinner />
      )}
      <DrillHistoryDrawer
        visible={isHistoryDrawerVisible}
        onClose={() => setIsHistoryDrawerVisible(false)}
        vesselDrills={drillMatrixData?.drills ?? []}
        reports={drillReports}
        onReportPress={() => alert("Report pressed")}
      />
      <ModifyDrillReportDrawer
        mode={DrawerMode.Create}
        visible={isCreateDrillReportDrawerVisible}
        onClose={() => setIsCreateDrillReportDrawerVisible(false)}
      />
    </ScrollablePageLayout>
  );
};

export default Drills;
