import React, { useMemo } from "react";
import { sharedState } from "@src/shared-state/shared-state";
import { ScrollView, StyleSheet, Text } from "react-native";
import {
  SeaPageCard,
  SubNav,
} from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import { SeaSettingsButton } from "@src/components/_molecules/IconButtons/SeaSettingsButton";
import { useSafetySubNav } from "@src/hooks/useSubNav";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import {
  SeaFilterTags,
  SeaFilterTagsValue,
} from "@src/components/_atoms/SeaFilterTags/SeaFilterTags";
import { SafetyEquipmentExpiriesTable } from "@src/components/_organisms/Safety/SafetyEquipmentExpiries/SafetyEquipmentExpiriesTable";
import { SafetyEquipmentItem } from "@src/shared-state/VesselSafety/safetyEquipmentItems";
import { formatDate, formatDateShort, warnDays } from "@src/lib/datesAndTime";
import { useStatusFilter } from "@src/hooks/useStatusFilter";
import { extractSearchTerms } from "@src/lib/util";
import { renderCategoryName } from "@src/lib/categories";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { useRouter } from "expo-router";
import { ModifySafetyEquipmentExpiryDrawer } from "@src/components/_organisms/Safety/SafetyEquipmentExpiries/ModifySafetyEquipmentExpiryDrawer";
import { DrawerMode } from "@src/components/_atoms/SeaDrawer/SeaDrawer";
import { SeaFilterSearch } from "@src/components/_atoms/SeaFilterSearch/SeaFilterSearch";
import { SeaLoadingSpinner } from "@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner";
import { ScrollablePageLayout } from "@src/layout/ScrollablePageLayout/ScrollablePageLayout";

interface SafetyEquipmentExpiriesProps {
  vesselId?: string;
  visible: boolean;
  headerSubNavigation?: SubNav[];
}

const SafetyEquipmentExpiriesPage = ({
  vesselId,
  visible,
  headerSubNavigation,
}: SafetyEquipmentExpiriesProps) => {
  const vessel = sharedState.vessel.use();
  const safetySubNav = useSafetySubNav(vessel?.id);

  // Global State
  const safetyEquipmentItems = sharedState.safetyEquipmentItems.use();
  const vesselSafetyItems = sharedState.vesselSafetyItems.use();
  const vesselLocations = sharedState.vesselLocations.use();

  // Internal State
  const [seaFilterTagsValue, setSeaFilterTagsValue] = React.useState<
    Partial<SeaFilterTagsValue>
  >({
    all: { isActive: false },
    overdue: { isActive: true },
    upcoming: { isActive: true },
    critical: { isActive: false },
  });
  const [searchValue, setSearchValue] = React.useState("");
  const [createItemModalVisible, setCreateItemModalVisible] =
    React.useState(false);

  const allItems = useMemo(() => {
    if (!safetyEquipmentItems) {
      return [];
    }

    const all = safetyEquipmentItems.all;

    const combined = [...all.servicable, ...all.expiring, ...all.nonExpiring];

    return combined.filter((x) => x.state === "active");
  }, [safetyEquipmentItems]);

  const { overdue, upcoming } = useStatusFilter(
    allItems,
    (item: SafetyEquipmentItem) => item.dateDue,
    warnDays.safetyEquipmentExpiries[0],
  );

  const criticalItems = useMemo(() => {
    if (!allItems || !vesselSafetyItems) {
      console.log("MISSING STUFF");
      return [];
    }
    const result = allItems.filter(
      (item) => vesselSafetyItems?.byId[item.itemId]?.isCritical,
    );
    console.log("CRITICAL", { result });
    return result;
  }, [allItems, vesselSafetyItems]);

  const filterCounts = useMemo(() => {
    return {
      all: allItems.length ?? 0,
      overdue: overdue.length,
      upcoming: upcoming.length,
      critical: criticalItems.length,
    };
  }, [allItems, overdue, upcoming, criticalItems]);

  // Effects and Memos

  const combinedFilterValues = useMemo<Partial<SeaFilterTagsValue>>(() => {
    return {
      all: {
        isActive: seaFilterTagsValue.all?.isActive ?? false,
        count: filterCounts.all,
      },
      overdue: {
        isActive: seaFilterTagsValue.overdue?.isActive ?? false,
        count: filterCounts.overdue,
      },
      upcoming: {
        isActive: seaFilterTagsValue.upcoming?.isActive ?? false,
        count: filterCounts.upcoming,
      },
      critical: {
        isActive: seaFilterTagsValue.critical?.isActive ?? false,
        count: filterCounts.critical,
      },
    };
  }, [seaFilterTagsValue, filterCounts]);

  const router = useRouter();

  const tableItems = useMemo(() => {
    const isCritical = (itemId: string) =>
      vesselSafetyItems?.byId[itemId]?.isCritical ?? false;

    if (seaFilterTagsValue.all?.isActive) {
      return allItems;
    }

    let result = [
      ...(seaFilterTagsValue.overdue?.isActive ? overdue : []),
      ...(seaFilterTagsValue.upcoming?.isActive ? upcoming : []),
    ];

    // Filter to show only critical if critical is selected
    if (seaFilterTagsValue.critical?.isActive) {
      result = result.filter((x) =>
        seaFilterTagsValue.critical?.isActive ? isCritical(x.itemId) : true,
      );
    }

    // Filter to only show items including search value
    if (searchValue) {
      const searchTerms = extractSearchTerms(searchValue, true);

      result = result.filter((item) => {
        const searchableValues = [
          renderCategoryName(item.itemId, vesselSafetyItems), // Name
          renderCategoryName(item.locationId, vesselLocations), // Location
          formatDateShort(item.whenLastChecked), // Last Service
          formatDate(item.dateDue), // Next Check
          item.type, // Servicable / Expiring / Non-Expiring
        ];

        return searchTerms.some((term) =>
          searchableValues.some((val) => val?.toLowerCase().includes(term)),
        );
      });
    }

    return result;
  }, [
    allItems,
    seaFilterTagsValue,
    searchValue,
    overdue,
    upcoming,
    vesselSafetyItems,
  ]);

  // Handlers
  const onSelectItem = (item: SafetyEquipmentItem) => {
    return router.navigate({
      pathname: getRoutePath(Routes.SAFETY_EQUIPMENT_EXPIRIES_VIEW),
      params: {
        itemId: item.id,
        vesselId: vesselId,
      },
    });
  };

  const onAddNewItem = () => {
    setCreateItemModalVisible(true);
  };

  // Loading state
  if (!safetyEquipmentItems || !vesselSafetyItems || !vesselLocations) {
    return <SeaLoadingSpinner variant={"lifeBuoy"} />;
  }

  return (
    <ScrollablePageLayout>
      <RequirePermissions
        role={"safetyEquipmentList"}
        level={permissionLevels.VIEW}
        showDenial={true}
      >
        <SeaPageCard
          title="Safety Equipment Expiries"
          primaryActionButton={
            <SeaButton
              onPress={onAddNewItem}
              variant={SeaButtonVariant.Primary}
              label={"Add New"}
              iconOptions={{ icon: "add" }}
            />
          }
          secondaryActionButton={[
            <SeaDownloadButton
              key={"download"}
              onPress={() => alert("This functionality is not completed yet")}
            />,
            <SeaSettingsButton
              key={"settings"}
              onPress={() => alert("This functionality is not completed yet")}
            />,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Filter Row */}
        <SeaStack
          isCollapsible
          direction={"row"}
          justify={"between"}
          gap={10}
          style={styles.filterRow}
        >
          <SeaFilterTags
            value={combinedFilterValues}
            onChange={(value) => {
              setSeaFilterTagsValue({
                all: { isActive: value.all?.isActive ?? false },
                overdue: { isActive: value.overdue?.isActive ?? false },
                upcoming: { isActive: value.upcoming?.isActive ?? false },
                critical: { isActive: value.critical?.isActive ?? false },
              });
            }}
            style={{ width: "100%", flex: 1 }}
          />
          <SeaFilterSearch
            value={searchValue}
            onChangeText={setSearchValue}
            style={{ width: "100%", flex: 1 }}
          />
        </SeaStack>

        <SafetyEquipmentExpiriesTable
          items={tableItems}
          vesselSafetyItems={vesselSafetyItems}
          vesselLocations={vesselLocations}
          onSelect={onSelectItem}
          showGrouped={true} // We always show the grouping for Safety Equipment Expiries
        />
        <ModifySafetyEquipmentExpiryDrawer
          mode={DrawerMode.Create}
          visible={createItemModalVisible}
          onClose={() => setCreateItemModalVisible(false)}
        />
      </RequirePermissions>
    </ScrollablePageLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    height: "100%",
    paddingTop: 0,
  },
  filterRow: { paddingHorizontal: 12 },
});

export default SafetyEquipmentExpiriesPage;
