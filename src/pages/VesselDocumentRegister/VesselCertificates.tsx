import { View, Text } from "react-native";
import React, { useMemo, useState } from "react";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { ScrollView } from "react-native-gesture-handler";
import {
  SeaPageCard,
  SubNav,
} from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import { SeaSettingsButton } from "@src/components/_molecules/IconButtons/SeaSettingsButton";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import { VesselCertificate } from "@src/shared-state/VesselDocuments/vesselCertificates";
import SeaFileImage from "@src/components/_atoms/SeaFileImage/SeaFileImage";
import {
  formatDateShort,
  getDateDifferenceInDays,
  warnDays,
} from "@src/lib/datesAndTime";
import { WhenDueStatus } from "@src/components/_molecules/WhenDueStatus/WhenDueStatus";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";
import { usePermission } from "@src/hooks/usePermission";
import {
  SeaFilterTags,
  SeaFilterTagsValue,
} from "@src/components/_atoms/SeaFilterTags/SeaFilterTags";
import { sharedState } from "@src/shared-state/shared-state";
import { DateTime } from "luxon";
import { CategoriesData, renderCategoryName } from "@src/lib/categories";
import { useRouter } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { EditVesselCertificateDrawer } from "@src/components/_organisms/VesselDocumentRegister/EditVesselCertificateDrawer";
import { SeaTableIcon } from "@src/components/_atoms/SeaTable/SeaTableIcon";
import { DrawerMode } from "@src/components/_atoms/SeaDrawer/SeaDrawer";
import { ScrollablePageLayout } from "@src/layout/ScrollablePageLayout/ScrollablePageLayout";

interface VesselCertificatesProps {
  headerSubNavigation?: SubNav[];
}

export function VesselCertificates({
  headerSubNavigation,
}: VesselCertificatesProps) {
  const vessel = sharedState.vessel.use();
  const vesselCertificates = sharedState.vesselCertificates.use();
  const vesselCertificateCategories =
    sharedState.vesselCertificateCategories.use();

  const { styles } = useStyles(styleSheet);
  const router = useRouter();

  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(false);
  const [seaFilterTagsValue, setSeaFilterTagsValue] = useState<
    Partial<SeaFilterTagsValue>
  >({
    all: { isActive: false },
    overdue: { isActive: true },
    upcoming: { isActive: true },
  });

  const allData = useMemo(() => {
    const data = vesselCertificates?.all;

    setSeaFilterTagsValue((prev) => ({
      ...prev,
      all: { isActive: prev.all?.isActive ?? false, count: data?.length ?? 0 },
    }));

    return data;
  }, [vesselCertificates]);

  const overDueData = useMemo(() => {
    const data = vesselCertificates?.prioritised.filter((cert) => {
      const diff = cert.dateExpires
        ? getDateDifferenceInDays(DateTime.fromISO(cert.dateExpires))
        : 0;

      return diff < 0; // TODO
    });

    setSeaFilterTagsValue((prev) => ({
      ...prev,
      overdue: {
        isActive: prev.overdue?.isActive ?? false,
        count: data?.length ?? 0,
      },
    }));

    return data;
  }, [vesselCertificates]);

  const upcomingData = useMemo(() => {
    const data = vesselCertificates?.prioritised.filter((cert) => {
      const diff = cert.dateExpires
        ? getDateDifferenceInDays(DateTime.fromISO(cert.dateExpires))
        : 0;

      return diff > 0 && diff < warnDays.vesselCertificates[0]; // TODO
    });

    setSeaFilterTagsValue((prev) => ({
      ...prev,
      upcoming: {
        isActive: prev.upcoming?.isActive ?? false,
        count: data?.length ?? 0,
      },
    }));

    return data;
  }, [vesselCertificates]);

  const filteredTasks = useMemo(() => {
    if (!vesselCertificates) return [];

    let data: VesselCertificate[] = [];

    if (seaFilterTagsValue.all?.isActive) {
      data = allData ?? [];
    } else {
      if (seaFilterTagsValue.overdue?.isActive) {
        data = [...data, ...(overDueData ?? [])];
      }

      if (seaFilterTagsValue.upcoming?.isActive) {
        data = [...data, ...(upcomingData ?? [])];
      }
    }

    return data ?? [];
  }, [allData, overDueData, seaFilterTagsValue, vesselCertificates]);

  const columns = useMemo(
    () => buildColumns(vessel?.isShoreFacility),
    [vessel],
  );

  const handlePress = (item: VesselCertificate) => {
    router.navigate({
      pathname: getRoutePath(Routes.VESSEL_CERTIFICATES_VIEW),
      params: {
        vesselId: vessel?.id,
        certificateId: item.id,
      },
    });
  };

  const rows = useMemo(
    () => buildRows(filteredTasks, handlePress, vesselCertificateCategories),
    [filteredTasks, vesselCertificateCategories],
  );

  return (
    <ScrollablePageLayout>
      <RequirePermissions
        role="vesselCertificates"
        level={permissionLevels.VIEW}
        showDenial={true}
      >
        <SeaPageCard
          title="Vessel Certificates"
          primaryActionButton={
            <RequirePermissions
              role="vesselCertificates"
              level={permissionLevels.CREATE}
            >
              <SeaButton
                onPress={() => setIsVisibleAddDrawer(true)}
                variant={SeaButtonVariant.Secondary}
                label={"Add New"}
                iconOptions={{ icon: "add" }}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <SeaDownloadButton
              key={"download"}
              onPress={() => alert("This functionality is not completed yet")}
            />,
            <RequirePermissions
              key={"settings"}
              role="vesselCertificates"
              level={permissionLevels.CREATE}
            >
              <SeaSettingsButton
                onPress={() => alert("This functionality is not completed yet")}
              />
            </RequirePermissions>,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Filter Tags */}
        <SeaFilterTags
          value={seaFilterTagsValue}
          onChange={(value) => setSeaFilterTagsValue(value)}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable
            columns={columns}
            rows={rows}
            showGroupedTable={seaFilterTagsValue.all?.isActive}
          />
        </View>

        {isVisibleAddDrawer && (
          <EditVesselCertificateDrawer
            onClose={() => setIsVisibleAddDrawer(false)}
            visible={isVisibleAddDrawer}
            type={DrawerMode.Create}
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  );
}

const buildColumns = (isShoreFacility?: boolean) => {
  return [
    {
      label: "",
      render: (row: VesselCertificate) => (
        <SeaFileImage files={row.files} size="tiny" />
      ),
      width: 50,
    },
    {
      label: "Name",
      value: (row: VesselCertificate) => row.title,
    },
    {
      label: `${isShoreFacility ? "Certification" : "Certificate"} #`,
      value: (row: VesselCertificate) => row.certNum,
      icon: () => <SeaTableIcon icon={"license"} />,
    },
    {
      label: "Issued By",
      value: (row: VesselCertificate) => row.issuedBy,
      icon: () => <SeaTableIcon icon={"fact_check"} />,
    },
    {
      label: "Expiry",
      value: (row: VesselCertificate) => formatDateShort(row.dateExpires),
      icon: () => <SeaTableIcon icon={"calendar_month"} />,
    },
    {
      label: "Status",
      render: (row: VesselCertificate) =>
        row.dateExpires ? (
          <WhenDueStatus
            whenDue={row.dateExpires}
            warnDaysThreshold={warnDays.vesselCertificates[0]}
            hasFault={false}
          />
        ) : (
          <></>
        ),
    },
    {
      label: "Reminder",
      render: (row: VesselCertificate) =>
        row.emailReminder ? (
          <SeaIcon icon="email" size={20} fill={false} />
        ) : null,
    },
  ] as SeaTableColumn<VesselCertificate>[];
};

const buildRows = (
  items: VesselCertificate[],
  onPress: (item: VesselCertificate) => void,
  vesselCertificateCategories?: CategoriesData,
) => {
  return items.map((item) => ({
    data: item,
    onPress: (item: VesselCertificate) => onPress(item),
    group: (item: VesselCertificate) =>
      renderCategoryName(item.categoryId, vesselCertificateCategories),
  }));
};

const styleSheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}));
