import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import {
  DrawerMode,
  SeaDrawer,
  SeaDrawerProps,
} from "@src/components/_atoms/SeaDrawer/SeaDrawer";

import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";

import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { FormikValues, useFormik } from "formik";
import {
  renderFullName,
  renderFullNameForUserId,
} from "@src/shared-state/Core/users";
import { formatSeaDate, subtractInterval } from "@src/lib/datesAndTime";
import { sharedState } from "@src/shared-state/shared-state";

import { makeDateTime } from "@src/lib/util";
import { SeaEmailReminderDropdown } from "@src/components/_atoms/SeaEmailReminderDropdown/SeaEmailReminderDropdown";
import { useServiceContainer } from "@src/providers/ServiceProvider";
import { SeaDropdown } from "@src/components/_atoms/SeaDropdown/SeaDropdown";
import { SeaTextInput } from "@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput";
import { SeaDateTimeInput } from "@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput";
import { SeaFileUploader } from "@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader";
import Yup, { notTooOld } from "@src/lib/yup";

import { SeaFile } from "@src/lib/fileImports";
import { CrewCertificate } from "@src/shared-state/Crew/crewCertificates";
import { renderCategoryName } from "@src/lib/categories";
import {
  UpdateCrewCertificateDto,
  UpdateCrewCertificateUseCase,
} from "@src/domain/use-cases/crewCertificates/UpdateCrewCertificateUseCase";
import {
  CreateCrewCertificateDto,
  CreateCrewCertificateUseCase,
} from "@src/domain/use-cases/crewCertificates/CreateCrewCertificateUseCase";

const MISSING_STATE = "missing";

const validationSchema = Yup.object({
  titleId: Yup.string().max(500).required(),
  heldBy: Yup.string().max(500).required(),
  issuedBy: Yup.string().max(500),
  dateIssued: Yup.date()
    .max(formatSeaDate())
    .required()
    .min(...notTooOld),
  type: Yup.string().max(200).required(),
  dateExpires: Yup.date().when("type", {
    is: "renewable",
    then: (schema) => schema.required().min(...notTooOld),
  }),
  emailReminder: Yup.string().when("type", {
    is: "renewable",
    then: (schema) => schema.max(200),
  }),
});

export interface EditVesselCertificateDrawerProps
  extends Pick<SeaDrawerProps, "visible" | "onClose" | "style"> {
  selectedItem?: CrewCertificate;
  type?: DrawerMode;
}
export function EditCrewCertificateDrawer({
  selectedItem,
  visible,
  onClose,
  type = DrawerMode.Edit,
  style,
}: EditVesselCertificateDrawerProps) {
  const userId = sharedState.userId.use(visible);
  const vessel = sharedState.vessel.use(visible);
  const vesselId = sharedState.vesselId.use(visible);
  const licenseeId = sharedState.licenseeId.use(visible);
  const crewCertificateTitles = sharedState.crewCertificateTitles.use(visible);
  const users = sharedState.users.use(visible);

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([]);
  const services = useServiceContainer();

  const certTypeOptions = useMemo(() => {
    return [
      {
        label: `Renewable Certificate`,
        value: "renewable",
      },
      {
        label: `Non-Expiring Certificate`,
        value: "nonExpiring",
      },
    ];
  }, [vessel]);

  const certificateTitlesOptions = useMemo(() => {
    if (!crewCertificateTitles) return [];

    return crewCertificateTitles.ids.map((option) => {
      const category = crewCertificateTitles?.byId[option];

      return {
        label: category.name,
        value: category.id,
      };
    });
  }, [crewCertificateTitles]);

  const crewMemberOptions = useMemo(() => {
    if (!users) return [];

    return users.staff.map((user) => ({
      label: renderFullNameForUserId(user.id),
      value: user.id,
    }));
  }, [users]);

  const initialValues = useMemo(() => {
    return {
      type:
        selectedItem?.state && selectedItem?.state !== MISSING_STATE
          ? selectedItem?.type
          : "renewable",
      dateExpires: selectedItem?.dateExpires ?? "",
      emailReminder: selectedItem?.emailReminder ?? "",
      titleId: selectedItem?.titleId ?? "",
      heldBy: selectedItem?.heldBy ?? "",
      issuedBy:
        selectedItem?.state === MISSING_STATE
          ? renderFullName()
          : selectedItem?.issuedBy,
      dateIssued:
        selectedItem?.state === MISSING_STATE
          ? formatSeaDate()
          : selectedItem?.dateIssued,
    };
  }, [selectedItem]);

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      console.debug(licenseeId, userId);
      if (!licenseeId || !userId) {
        console.error(
          "Vessel ID, Licensee ID, Vessel, or User ID is not available",
        );
        return;
      }

      let dateToRemind: string | undefined = undefined;
      if (
        values.type === "renewable" &&
        values.dateExpires &&
        values.emailReminder
      ) {
        dateToRemind =
          subtractInterval(
            values.dateExpires,
            values.emailReminder,
          ).toISODate() ?? undefined;
      }

      const commonDto = {
        vesselId: vesselId ?? "",
        title: renderCategoryName(values.titleId, crewCertificateTitles),
        titleId: values.titleId,
        issuedBy: values.issuedBy ?? undefined,
        dateIssued: formatSeaDate(values.dateIssued) ?? "",
        dateExpires: formatSeaDate(values.dateExpires) ?? undefined,
        emailReminder: values.emailReminder ?? undefined,
        dateToRemind,
        files: files,
      };

      if (type === "edit" && selectedItem?.state !== MISSING_STATE) {
        const dto: UpdateCrewCertificateDto = {
          ...commonDto,
          id: selectedItem?.id ?? "",
        };

        const updateCrewCertificate = services.get(
          UpdateCrewCertificateUseCase,
        );

        updateCrewCertificate
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch((err) =>
            console.error(`Error updating Crew Certificate\n ${err.message}`),
          );
      } else {
        const dto: CreateCrewCertificateDto = {
          ...commonDto,
          type: values.type,
          heldBy: values.heldBy,
        };
        const createVesselCertificate = services.get(
          CreateCrewCertificateUseCase,
        );
        createVesselCertificate
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch((err) =>
            console.error(`Error creating Vessel Certificate\n ${err.message}`),
          );
      }
    },
    [files, vesselId, licenseeId, userId],
  );

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: (values) => handleSubmit(values),
  });

  return (
    <SeaDrawer
      title={`${selectedItem?.state === MISSING_STATE ? "Add Missing" : type === "create" ? "Add" : "Edit"} Certification`}
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        <SeaButton
          label={"Save Completed Task"}
          variant={SeaButtonVariant.Primary}
          key="Save Completed Task"
          onPress={formik.handleSubmit}
        />
      }
    >
      <SeaStack direction="column" gap={10} justify="start" align="start">
        <SeaDropdown
          label={"Certificate Title"}
          items={certificateTitlesOptions}
          style={{
            width: "100%",
          }}
          onSelect={(value) => formik.setFieldValue("titleId", value)}
          value={formik.values.titleId}
          errorText={formik.errors.titleId ?? undefined}
          hasError={!!formik.errors.titleId}
        />

        <SeaDropdown
          label={"Crew Member"}
          items={crewMemberOptions}
          style={{
            width: "100%",
          }}
          onSelect={(value) => formik.setFieldValue("heldBy", value)}
          value={formik.values.heldBy}
          disabled={type === "edit" || selectedItem?.state === MISSING_STATE}
          errorText={formik.errors.heldBy ?? undefined}
          hasError={!!formik.errors.heldBy}
        />

        <SeaTextInput
          label="Issued By"
          value={formik.values.issuedBy ?? ""}
          onChangeText={formik.handleChange("issuedBy")}
          errorText={formik.errors.issuedBy ?? undefined}
          hasError={!!formik.errors.issuedBy}
        />

        <SeaDateTimeInput
          value={makeDateTime(formik.values.dateIssued)}
          onChange={(date) => formik.setFieldValue("dateIssued", date)}
          type={"datetime"}
          label="Issue Date"
          style={{ flex: 1 }}
          errorText={formik.errors.dateIssued ?? undefined}
          hasError={!!formik.errors.dateIssued}
        />

        <SeaDropdown
          label={"Certificate Type"}
          items={certTypeOptions}
          style={{
            width: "100%",
          }}
          onSelect={(value) => formik.setFieldValue("type", value)}
          value={formik.values.type}
          disabled={type === "edit"}
          errorText={formik.errors.type ?? undefined}
          hasError={!!formik.errors.type}
        />

        {formik.values.type.includes("renewable") && (
          <>
            <SeaDateTimeInput
              value={makeDateTime(formik.values.dateExpires)}
              onChange={(date) => formik.setFieldValue("dateExpires", date)}
              type={"datetime"}
              label="Expiry Date"
              style={{ flex: 1 }}
              errorText={formik.errors.dateExpires ?? undefined}
              hasError={!!formik.errors.dateExpires}
            />

            <SeaEmailReminderDropdown
              label="Email Reminder"
              value={formik.values.emailReminder}
              onSelect={(value) => formik.setFieldValue("emailReminder", value)}
              style={{ flex: 1, width: "100%" }}
              errorText={formik.errors.emailReminder ?? undefined}
              hasError={!!formik.errors.emailReminder}
            />
          </>
        )}
        <SeaFileUploader
          initialFiles={selectedItem?.files}
          files={files}
          setFiles={setFiles}
        />
      </SeaStack>
    </SeaDrawer>
  );
}
