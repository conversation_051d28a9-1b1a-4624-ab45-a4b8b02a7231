import { View, Text } from "react-native";
import React, { useC<PERSON>back, useMemo, useState } from "react";
import { sharedState } from "@src/shared-state/shared-state";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { ScrollView } from "react-native-gesture-handler";
import { SeaPageCard } from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import { SeaEditButton } from "@src/components/_molecules/IconButtons/SeaEditButton";
import { SeaDeleteButton } from "@src/components/_molecules/IconButtons/SeaDeleteButton";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { SeaLabelValue } from "@src/components/_atoms/SeaLabelValue/SeaLabelValue";
import { useGlobalSearchParams } from "expo-router";
import { useDeviceWidth } from "@src/hooks/useDevice";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import { formatValue } from "@src/lib/util";
import { formatEmailReminder } from "@src/lib/datesAndTime";
import { SeaButton } from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import { SeaMedia } from "@src/components/_molecules/SeaMedia/SeaMedia";
import { getFileNameWithExtension, isPdf } from "@src/lib/files";
import { MediaCardFile } from "@src/components/_atoms/SeaMediaCard/SeaMediaCard";
import { DrawerMode } from "@src/components/_atoms/SeaDrawer/SeaDrawer";
import { renderFullNameForUserId } from "@src/shared-state/Core/users";
import { renderCategoryName } from "@src/lib/categories";
import { EditCrewCertificateDrawer } from "./EditCrewCertificateDrawer";
import { RenewCrewCertificateDrawer } from "./RenewCrewCertificateDrawer";
import { SeaPDFReader } from "@src/components/_atoms/SeaPDFReader/SeaPDFReader";

const DESKTOP_ITEMS_WIDTH = "100%";

export function ViewCrewCertificates() {
  const crewCertificates = sharedState.crewCertificates.use();
  const crewCertificateTitles = sharedState.crewCertificateTitles.use();

  const { certificateId } = useGlobalSearchParams();
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth();
  const { styles } = useStyles(styleSheet);

  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false);
  const [isVisibleRenewDrawer, setIsVisibleRenewDrawer] = useState(false);

  const selectedItem = useMemo(() => {
    return crewCertificates?.byId[
      Array.isArray(certificateId) ? certificateId[0] : certificateId
    ];
  }, [certificateId, crewCertificates]);

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return [];

    return selectedItem?.files.map((file) => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [
        <SeaDownloadButton
          key={`download-${file}`}
          onPress={() => alert("Coming soon!")}
        />,
      ],
    })) as MediaCardFile[];
  }, [selectedItem]);

  const isPDFCheck = useCallback((file?: string) => {
    if (!file) return false;

    return isPdf(file);
  }, []);

  return (
    <RequirePermissions
      role="crewCertificates"
      level={permissionLevels.VIEW}
      showDenial={true}
    >
      <ScrollView style={styles.container}>
        <SeaPageCard
          primaryActionButton={
            selectedItem?.type === "renewable" ? (
              <SeaButton
                label="Renew Certificate"
                onPress={() => setIsVisibleRenewDrawer(true)}
              />
            ) : undefined
          }
          secondaryActionButton={[
            <RequirePermissions
              key={"Edit"}
              role="crewCertificates"
              level={permissionLevels.EDIT}
            >
              <SeaEditButton onPress={() => setIsVisibleEditDrawer(true)} />
            </RequirePermissions>,
            <RequirePermissions
              key={"Delete"}
              role="crewCertificates"
              level={permissionLevels.FULL}
            >
              <SeaDeleteButton onPress={() => alert("Coming soon!")} />
            </RequirePermissions>,
          ]}
        >
          <SeaTypography variant="title">
            {renderFullNameForUserId(selectedItem?.heldBy)} -{" "}
            {renderCategoryName(selectedItem?.titleId, crewCertificateTitles)}
          </SeaTypography>
          <SeaStack
            direction={isLargeDesktopWidth ? "row" : "column"}
            gap={20}
            justify="start"
            align="start"
          >
            <SeaStack
              direction="column"
              gap={isDesktopWidth ? 20 : 0}
              align={"start"}
              width={isLargeDesktopWidth ? "60%" : "100%"}
            >
              <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                <SeaLabelValue
                  label={"Issued By"}
                  value={selectedItem?.issuedBy}
                />

                <SeaLabelValue
                  label={"Issued Date"}
                  value={selectedItem?.dateIssued}
                />
              </SeaStack>

              {selectedItem?.type === "renewable" && (
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue
                    label={"Expiry"}
                    value={selectedItem?.dateExpires}
                  />

                  <SeaLabelValue
                    label={"Notification"}
                    value={formatValue(
                      formatEmailReminder(selectedItem?.emailReminder),
                    )}
                  />
                </SeaStack>
              )}
            </SeaStack>
            {uploadedFiles.length > 0 && (
              <View style={{ flex: 1 }}>
                <SeaMedia type="manuals" title="Files" files={uploadedFiles} />
              </View>
            )}
          </SeaStack>
        </SeaPageCard>

        {isPDFCheck(selectedItem?.files?.[0]) ? (
          <SeaPDFReader file={selectedItem?.files?.[0]} />
        ) : (
          <></>
        )}
      </ScrollView>

      {isVisibleEditDrawer && (
        <EditCrewCertificateDrawer
          onClose={() => setIsVisibleEditDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleEditDrawer}
          type={DrawerMode.Edit}
        />
      )}

      {isVisibleRenewDrawer && (
        <RenewCrewCertificateDrawer
          onClose={() => setIsVisibleRenewDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleRenewDrawer}
        />
      )}
    </RequirePermissions>
  );
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
  content: {
    marginBottom: 20,
  },
}));
