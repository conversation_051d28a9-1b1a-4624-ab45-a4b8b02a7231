import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import {
  SeaDrawer,
  SeaDrawerProps,
} from "@src/components/_atoms/SeaDrawer/SeaDrawer";

import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";

import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { FormikValues, useFormik } from "formik";
import { formatSeaDate, subtractInterval } from "@src/lib/datesAndTime";
import { sharedState } from "@src/shared-state/shared-state";

import { makeDateTime } from "@src/lib/util";
import { useServiceContainer } from "@src/providers/ServiceProvider";
import { useRouter } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { SeaDateTimeInput } from "@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput";
import { SeaFileUploader } from "@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader";
import { SeaFile } from "@src/lib/fileImports";
import { CrewCertificate } from "@src/shared-state/Crew/crewCertificates";
import { renderCategoryName } from "@src/lib/categories";
import {
  RenewCrewCertificateDto,
  RenewCrewCertificateUseCase,
} from "@src/domain/use-cases/crewCertificates/RenewCrewCertificateUseCase";

export interface EditVesselCertificateDrawerProps
  extends Pick<SeaDrawerProps, "visible" | "onClose" | "style"> {
  selectedItem?: CrewCertificate;
}
export function RenewCrewCertificateDrawer({
  selectedItem,
  visible,
  onClose,
  style,
}: EditVesselCertificateDrawerProps) {
  const userId = sharedState.userId.use(visible);
  const vesselId = sharedState.vesselId.use(visible);
  const licenseeId = sharedState.licenseeId.use(visible);
  const crewCertificateTitles = sharedState.crewCertificateTitles.use(visible);

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([]);
  const router = useRouter();
  const services = useServiceContainer();

  const initialValues = useMemo(() => {
    return {
      dateIssued: formatSeaDate(),
      dateExpires: "",
    };
  }, [selectedItem]);

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!licenseeId || !userId || !selectedItem) {
        console.error(
          "Crew Certificate, Licensee ID, or User ID is not available",
        );
        return;
      }

      let dateToRemind: string | undefined = undefined;
      if (
        selectedItem.type === "renewable" &&
        values.dateExpires &&
        selectedItem.emailReminder
      ) {
        dateToRemind =
          subtractInterval(
            values.dateExpires,
            selectedItem.emailReminder,
          ).toISODate() ?? undefined;
      }

      const dto: RenewCrewCertificateDto = {
        id: selectedItem.id,
        heldBy: selectedItem.heldBy, // Add heldBy from selectedItem or appropriate value
        type: selectedItem.type, // Add type from selectedItem or appropriate value
        vesselId: vesselId ?? "",
        title: renderCategoryName(selectedItem.titleId, crewCertificateTitles),
        titleId: selectedItem.titleId,
        issuedBy: selectedItem.issuedBy ?? undefined,
        dateIssued: formatSeaDate(values.dateIssued) ?? "",
        dateExpires: formatSeaDate(values.dateExpires) ?? undefined,
        emailReminder: selectedItem.emailReminder ?? undefined,
        dateToRemind,
        files: files,
      };

      const renewVesselCertificate = services.get(RenewCrewCertificateUseCase);

      renewVesselCertificate
        .execute(dto, userId, licenseeId)
        .then(() => {
          onClose();
          router.navigate({
            pathname: getRoutePath(Routes.CREW_CERTIFICATES),
            params: {
              vesselId: vesselId,
            },
          });
        })
        .catch((err) =>
          console.error(`Error renewing Crew Certificate\n ${err.message}`),
        );
    },
    [crewCertificateTitles, selectedItem, files, vesselId],
  );

  const formik = useFormik({
    initialValues,
    // validationSchema,
    onSubmit: (values) => handleSubmit(values),
  });

  return (
    <SeaDrawer
      title={`Renew Certificate: ${selectedItem?.title}`}
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        <SeaButton
          label={"Save Completed Task"}
          variant={SeaButtonVariant.Primary}
          key="Save Completed Task"
          onPress={formik.handleSubmit}
        />
      }
    >
      <SeaStack direction="column" gap={10} justify="start" align="start">
        <SeaDateTimeInput
          value={makeDateTime(formik.values.dateIssued)}
          onChange={(date) => formik.setFieldValue("dateIssued", date)}
          type={"datetime"}
          label="Issue Date"
          style={{ flex: 1 }}
        />

        <SeaDateTimeInput
          value={makeDateTime(formik.values.dateExpires)}
          onChange={(date) => formik.setFieldValue("dateExpires", date)}
          type={"datetime"}
          label="New Expiry Date"
          style={{ flex: 1 }}
        />

        <SeaFileUploader initialFiles={[]} files={files} setFiles={setFiles} />
      </SeaStack>
    </SeaDrawer>
  );
}
