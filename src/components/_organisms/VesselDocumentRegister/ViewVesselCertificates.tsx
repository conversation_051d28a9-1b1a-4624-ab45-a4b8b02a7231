import { View, Text } from "react-native";
import React, { useC<PERSON>back, useMemo, useState } from "react";
import { sharedState } from "@src/shared-state/shared-state";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { ScrollView } from "react-native-gesture-handler";
import { SeaPageCard } from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import { SeaEditButton } from "@src/components/_molecules/IconButtons/SeaEditButton";
import { SeaDeleteButton } from "@src/components/_molecules/IconButtons/SeaDeleteButton";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { SeaLabelValue } from "@src/components/_atoms/SeaLabelValue/SeaLabelValue";
import { useGlobalSearchParams } from "expo-router";
import { useDeviceWidth } from "@src/hooks/useDevice";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import { formatValue } from "@src/lib/util";
import { formatEmailReminder } from "@src/lib/datesAndTime";
import { SeaButton } from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import { SeaMedia } from "@src/components/_molecules/SeaMedia/SeaMedia";
import { getFileNameWithExtension, isPdf } from "@src/lib/files";
import { MediaCardFile } from "@src/components/_atoms/SeaMediaCard/SeaMediaCard";
import { EditVesselCertificateDrawer } from "./EditVesselCertificateDrawer";
import { RenewVesselCertificateDrawer } from "./RenewVesselCertificateDrawer";
import { DrawerMode } from "@src/components/_atoms/SeaDrawer/SeaDrawer";
import { SeaPDFReader } from "@src/components/_atoms/SeaPDFReader/SeaPDFReader";

const DESKTOP_ITEMS_WIDTH = "100%";

export function ViewVesselCertificates() {
  const userId = sharedState.userId.use();
  const vessel = sharedState.vessel.use();
  const vesselCertificateCategories =
    sharedState.vesselCertificateCategories.use();
  const vesselCertificates = sharedState.vesselCertificates.use();

  const { certificateId } = useGlobalSearchParams();
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth();
  const { styles } = useStyles(styleSheet);

  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false);
  const [isVisibleRenewDrawer, setIsVisibleRenewDrawer] = useState(false);

  const selectedItem = useMemo(() => {
    return vesselCertificates?.byId[
      Array.isArray(certificateId) ? certificateId[0] : certificateId
    ];
  }, [certificateId, vesselCertificates]);

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return [];

    return selectedItem?.files.map((file) => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [
        <SeaDownloadButton
          key={`download-${file}`}
          onPress={() => alert("Coming soon!")}
        />,
      ],
    })) as MediaCardFile[];
  }, [selectedItem]);

  const isPDFCheck = useCallback((file?: string) => {
    if (!file) return false;

    return isPdf(file);
  }, []);

  return (
    <RequirePermissions
      role="vesselCertificates"
      level={permissionLevels.VIEW}
      showDenial={true}
    >
      <ScrollView style={styles.container}>
        <SeaPageCard
          primaryActionButton={
            <SeaButton
              label="Renew Certificate"
              onPress={() => setIsVisibleRenewDrawer(true)}
            />
          }
          secondaryActionButton={[
            <SeaEditButton
              key={"Edit"}
              onPress={() => setIsVisibleEditDrawer(true)}
            />,
            <SeaDeleteButton
              key={"Delete"}
              onPress={() => alert("Coming soon!")}
            />,
            <SeaDownloadButton key={"Download"} />,
          ]}
        >
          <SeaTypography variant="title">{selectedItem?.title}</SeaTypography>
          <SeaStack
            direction={isLargeDesktopWidth ? "row" : "column"}
            gap={20}
            justify="start"
            align="start"
          >
            <SeaStack
              direction="column"
              gap={isDesktopWidth ? 20 : 0}
              align={"start"}
              width={isLargeDesktopWidth ? "60%" : "100%"}
            >
              <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                <SeaLabelValue
                  label={`${vessel?.isShoreFacility ? "Certification" : "Certificate"}`}
                  value={selectedItem?.certNum}
                />

                <SeaLabelValue
                  label={"Issued By"}
                  value={selectedItem?.issuedBy}
                />
              </SeaStack>

              <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                <SeaLabelValue
                  label={"Issued Date"}
                  value={selectedItem?.dateIssued}
                />

                {selectedItem?.categoryId &&
                vesselCertificateCategories?.byId ? (
                  <SeaLabelValue
                    label={"Category"}
                    value={formatValue(
                      vesselCertificateCategories.byId[selectedItem.categoryId]
                        .name,
                    )}
                  />
                ) : null}
              </SeaStack>

              {selectedItem?.type === "renewable" && (
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue
                    label={"Expiry"}
                    value={selectedItem?.dateExpires}
                  />

                  <SeaLabelValue
                    label={"Notification"}
                    value={formatValue(
                      formatEmailReminder(selectedItem?.emailReminder),
                    )}
                  />
                </SeaStack>
              )}
            </SeaStack>
            {uploadedFiles.length > 0 && (
              <SeaMedia type="manuals" title="Files" files={uploadedFiles} />
            )}
          </SeaStack>
        </SeaPageCard>

        {isPDFCheck(selectedItem?.files?.[0]) ? (
          <SeaPDFReader file={selectedItem?.files?.[0]} />
        ) : (
          <></>
        )}
      </ScrollView>

      {isVisibleEditDrawer && (
        <EditVesselCertificateDrawer
          onClose={() => setIsVisibleEditDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleEditDrawer}
          type={DrawerMode.Edit}
        />
      )}

      {isVisibleRenewDrawer && (
        <RenewVesselCertificateDrawer
          onClose={() => setIsVisibleRenewDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleRenewDrawer}
        />
      )}
    </RequirePermissions>
  );
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
  content: {
    marginBottom: 20,
  },
}));
