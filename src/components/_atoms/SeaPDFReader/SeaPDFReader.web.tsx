import { getFileSrcFromString } from "@src/lib/files";
import {
  getCachedFileSrc,
  getCachedFileUri,
} from "@src/shared-state/FileSyncSystem/cachedFiles";
import { sharedState } from "@src/shared-state/shared-state";
import React, { useEffect, useState } from "react";
import { View } from "react-native";
import { SeaLoadingSpinner } from "../SeaLoadingSpinner/SeaLoadingSpinner";

export interface SeaPDFReaderProps {
  file?: string;
}

export const SeaPDFReader = ({ file }: SeaPDFReaderProps) => {
  const onlineStatus = sharedState.onlineStatus.use();
  const [fileUri, setFileUri] = useState<string>("");
  const [fileLoaded, setFileLoaded] = useState(false);

  useEffect(() => {
    if (!file || fileLoaded) return;

    let isMounted = true;
    let cancelFetch = false;

    const loadFileUri = async () => {
      let _uri = "";

      try {
        if (file.startsWith("data:application/pdf")) {
          _uri = file;
        } else if (!cancelFetch) {
          const uri = await getCachedFileUri(file);
          console.debug("SeaPDFReader getCachedFileSrc", uri);
          if (uri) {
            _uri = uri;
          }
        }
      } catch (error) {
        if (onlineStatus?.isOnline && !cancelFetch) {
          try {
            _uri = await getFileSrcFromString(file);
          } catch (error) {
            console.error("Failed to load PDF file from string", error);
          }
        } else {
          console.error("Offline or file not cached");
        }
      } finally {
        if (isMounted && _uri) {
          console.debug("SeaPDFReader loaded fileUri", _uri);
          setFileUri(_uri);
          setFileLoaded(true);
        }
      }
    };

    loadFileUri();

    return () => {
      isMounted = false;
      cancelFetch = true;
    };
  }, [file]);

  if (file && !fileUri) {
    return (
      <View
        style={{
          width: "100%",
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <SeaLoadingSpinner />
      </View>
    );
  }

  return fileUri ? (
    <View
      style={{
        flex: 1,
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <View
        style={{
          flex: 1,
          width: "100%",
          aspectRatio: 210 / 297,
          maxWidth: 680,
        }}
      >
        <embed
          src={`https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(fileUri)}`}
          type="application/pdf"
          style={{
            width: "100%",
            height: "100%",
          }}
        />
      </View>
    </View>
  ) : (
    <></>
  );
};
