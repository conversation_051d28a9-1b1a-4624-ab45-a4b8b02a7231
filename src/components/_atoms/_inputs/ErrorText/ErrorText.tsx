import { createStyleSheet, useStyles } from "@src/theme/styles";
import { Text } from "react-native";
import { SeaSpacer } from "@src/components/_atoms/SeaSpacer/SeaSpacer";
import React from "react";

export interface ErrorTextProps {
  hasError: boolean;
  text?: string;
  hideSpacer?: boolean;
}

export const ErrorText = ({
  hasError,
  text,
  hideSpacer = false,
}: ErrorTextProps) => {
  const { styles, theme } = useStyles(errorStyles);
  const errorText = text ?? "Error";

  return hasError ? (
    <Text style={styles.text}>{errorText}</Text>
  ) : !hideSpacer ? (
    <SeaSpacer height={18} />
  ) : (
    <></>
  );
};
const errorStyles = createStyleSheet((theme) => ({
  text: {
    color: theme.colors.status.errorPrimary,
    fontSize: 12,
    marginTop: 4,
  },
}));
