import { injectable } from "inversify";
import {
  FirestoreOperation,
  FirestoreRecord,
} from "../data/FirestoreOperation";
import { DocRef } from "../data/IFirestoreService";
import { UpdateVesselCertificateDto } from "../use-cases/vesselDocumentRegister/UpdateVesselCertificateUseCase";
import { CreateVesselCertificateDto } from "../use-cases/vesselDocumentRegister/CreateVesselCertificateUseCase";
import { RenewVesselCertificateDto } from "../use-cases/vesselDocumentRegister/RenewCertificateUseCase";

export interface IVesselCertificateService {
  updateVesselCertificate(
    operation: FirestoreOperation,
    updateJobListDto: UpdateVesselCertificateDto,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  };

  createVesselCertificate(
    operation: FirestoreOperation,
    createVesselCertificateDto: CreateVesselCertificateDto,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  };

  archiveVesselCertificate(
    operation: FirestoreOperation,
    vesselCertificateId: string,
    userId: string,
  ): FirestoreRecord;

  renewVesselCertificate(
    operation: FirestoreOperation,
    dto: RenewVesselCertificateDto,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  };
}

@injectable()
export class VesselCertificateService implements IVesselCertificateService {
  updateVesselCertificate(
    operation: FirestoreOperation,
    updateJobListDto: UpdateVesselCertificateDto,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  } {
    const {
      vesselId,
      id,
      certNum,
      issuedBy,
      dateExpires,
      emailReminder,
      categoryId,
      dateToRemind,
      ...rest
    } = updateJobListDto;

    const data = {
      ...rest,
      certNum: certNum ?? operation.deleteField(),
      issuedBy: issuedBy ?? operation.deleteField(),
      dateExpires: dateExpires ?? operation.deleteField(),
      emailReminder: emailReminder ?? operation.deleteField(),
      categoryId: categoryId ?? operation.deleteField(),
      dateToRemind: dateToRemind ?? operation.deleteField(),
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    };

    const updatedVesselCertificateRecord = {
      ref: operation.makeRef("vesselCertificates", id),
      data,
      options: { merge: true },
    };

    //TODO Create a new category if it doesn't exist

    const vesselTouched = this.vesselTouched(
      operation,
      "vesselCertificates",
      licenseeId,
      vesselId,
    );

    return {
      ref: updatedVesselCertificateRecord.ref,
      records: [updatedVesselCertificateRecord, vesselTouched],
    };
  }

  createVesselCertificate(
    operation: FirestoreOperation,
    createVesselCertificateDto: CreateVesselCertificateDto,
    userId: string,
    licenseeId: string,
  ): { ref: DocRef; records: FirestoreRecord[] } {
    const {
      certNum,
      issuedBy,
      dateExpires,
      emailReminder,
      categoryId,
      dateToRemind,
      ...rest
    } = createVesselCertificateDto;

    const data = {
      ...rest,
      ...(certNum ? { certNum } : {}),
      ...(issuedBy ? { issuedBy } : {}),
      ...(dateExpires ? { dateExpires } : {}),
      ...(emailReminder ? { emailReminder } : {}),
      ...(categoryId ? { categoryId } : {}),
      ...(dateToRemind ? { dateToRemind } : {}),
      state: "active",
      addedBy: userId,
      whenAdded: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    };

    const updatedVesselCertificateRecord = {
      ref: operation.makeRef("vesselCertificates"),
      data,
    };

    //TODO Create a new category if it doesn't exist

    const vesselTouched = this.vesselTouched(
      operation,
      "vesselCertificates",
      licenseeId,
      createVesselCertificateDto.vesselId,
    );

    return {
      ref: updatedVesselCertificateRecord.ref,
      records: [updatedVesselCertificateRecord, vesselTouched],
    };
  }

  archiveVesselCertificate(
    operation: FirestoreOperation,
    vesselCertificateId: string,
    userId: string,
  ): FirestoreRecord {
    const data = {
      state: "archived",
      archivedBy: userId,
      whenArchived: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    };

    const archivedVesselCertificateRecord = {
      ref: operation.makeRef("vesselCertificates", vesselCertificateId),
      data,
      options: { merge: true },
    };

    return archivedVesselCertificateRecord;
  }

  renewVesselCertificate(
    operation: FirestoreOperation,
    dto: Omit<RenewVesselCertificateDto, "id">,
    userId: string,
    licenseeId: string,
  ): { ref: DocRef; records: FirestoreRecord[] } {
    const {
      certNum,
      issuedBy,
      dateExpires,
      emailReminder,
      categoryId,
      dateToRemind,
      isShoreFacility,
      ...rest
    } = dto;

    const data = {
      ...rest,
      ...(certNum ? { certNum } : {}),
      ...(issuedBy ? { issuedBy } : {}),
      ...(dateExpires ? { dateExpires } : {}),
      ...(emailReminder ? { emailReminder } : {}),
      ...(categoryId ? { categoryId } : {}),
      ...(dateToRemind ? { dateToRemind } : {}),
      isShoreFacility: isShoreFacility ?? false,
      state: "active",
      addedBy: userId,
      wasRenewed: true,
      whenAdded: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    };

    console.debug(`Renewing vessel certificate with ID:`, data);

    const updatedVesselCertificateRecord = {
      ref: operation.makeRef("vesselCertificates"),
      data,
    };

    //TODO Create a new category if it doesn't exist

    const vesselTouched = this.vesselTouched(
      operation,
      "vesselCertificates",
      licenseeId,
      dto.vesselId,
    );

    return {
      ref: updatedVesselCertificateRecord.ref,
      records: [updatedVesselCertificateRecord, vesselTouched],
    };
  }

  private vesselTouched(
    operation: FirestoreOperation,
    collectionName: string,
    licenseeId: string,
    vesselId: string,
  ) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    };

    const vesselTouched = {
      ref: operation.makeRef("whenVesselTouched", vesselId),
      data: vesselData,
      options: { merge: true },
    };

    return vesselTouched;
  }
}
