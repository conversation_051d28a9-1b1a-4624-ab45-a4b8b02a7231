import { injectable } from "inversify";
import {
  FirestoreOperation,
  FirestoreRecord,
} from "../data/FirestoreOperation";
import { UpdateCrewCertificateDto } from "../use-cases/crewCertificates/UpdateCrewCertificateUseCase";
import { DocRef } from "../data/IFirestoreService";
import { CreateCrewCertificateDto } from "../use-cases/crewCertificates/CreateCrewCertificateUseCase";
import { RenewCrewCertificateDto } from "../use-cases/crewCertificates/RenewCrewCertificateUseCase";

export interface ICrewCertificateService {
  updateCrewCertificate(
    operation: FirestoreOperation,
    updateCrewCertificateDto: Omit<
      UpdateCrewCertificateDto,
      "title" | "vesselId"
    >,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  };

  createCrewCertificate(
    operation: FirestoreOperation,
    createCrewCertificateDto: Omit<
      CreateCrewCertificateDto,
      "title" | "vesselId"
    >,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  };

  archiveCrewCertificate(
    operation: FirestoreOperation,
    vesselCertificateId: string,
    userId: string,
  ): FirestoreRecord;

  renewCrewCertificate(
    operation: FirestoreOperation,
    dto: Omit<RenewCrewCertificateDto, "id" | "vesselId">,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  };
}

@injectable()
export class CrewCertificateService implements ICrewCertificateService {
  updateCrewCertificate(
    operation: FirestoreOperation,
    updateCrewCertificateDto: Omit<
      UpdateCrewCertificateDto,
      "title" | "vesselId"
    >,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  } {
    const { id, issuedBy, dateExpires, emailReminder, dateToRemind, ...rest } =
      updateCrewCertificateDto;

    const data = {
      ...rest,
      issuedBy: issuedBy ?? operation.deleteField(),
      dateExpires: dateExpires ?? operation.deleteField(),
      emailReminder: emailReminder ?? operation.deleteField(),
      dateToRemind: dateToRemind ?? operation.deleteField(),
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    };

    const updatedCrewCertificateRecord = {
      ref: operation.makeRef("crewCertificates", id),
      data,
      options: { merge: true },
    };

    const licenseeTouched = this.licenseeTouched(operation, licenseeId);

    return {
      ref: updatedCrewCertificateRecord.ref,
      records: [updatedCrewCertificateRecord, licenseeTouched],
    };
  }

  createCrewCertificate(
    operation: FirestoreOperation,
    createCrewCertificateDto: Omit<
      CreateCrewCertificateDto,
      "title" | "vesselId"
    >,
    userId: string,
    licenseeId: string,
  ): { ref: DocRef; records: FirestoreRecord[] } {
    const {
      issuedBy,
      dateExpires,
      emailReminder,
      dateToRemind,
      type,
      ...rest
    } = createCrewCertificateDto;

    const data = {
      ...rest,
      ...(issuedBy ? { issuedBy } : {}),
      ...(dateExpires && type === "renewable" ? { dateExpires } : {}),
      ...(emailReminder && type === "renewable" ? { emailReminder } : {}),
      ...(dateToRemind ? { dateToRemind } : {}),
      type,
      licenseeId,
      state: "active",
      addedBy: userId,
      whenAdded: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    };

    const updatedVesselCertificateRecord = {
      ref: operation.makeRef("crewCertificates"),
      data,
    };

    //TODO Create a new category if it doesn't exist
    const licenseeTouched = this.licenseeTouched(operation, licenseeId);

    return {
      ref: updatedVesselCertificateRecord.ref,
      records: [updatedVesselCertificateRecord, licenseeTouched],
    };
  }

  archiveCrewCertificate(
    operation: FirestoreOperation,
    crewCertificateId: string,
    userId: string,
  ): FirestoreRecord {
    const data = {
      state: "archived",
      archivedBy: userId,
      whenArchived: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    };

    const archivedVesselCertificateRecord = {
      ref: operation.makeRef("crewCertificates", crewCertificateId),
      data,
      options: { merge: true },
    };

    return archivedVesselCertificateRecord;
  }

  renewCrewCertificate(
    operation: FirestoreOperation,
    dto: Omit<RenewCrewCertificateDto, "id" | "vesselId">,
    userId: string,
    licenseeId: string,
  ): { ref: DocRef; records: FirestoreRecord[] } {
    const {
      issuedBy,
      dateExpires,
      emailReminder,
      dateToRemind,
      type,
      ...rest
    } = dto;

    const data = {
      ...rest,
      ...(issuedBy ? { issuedBy } : {}),
      ...(dateExpires ? { dateExpires } : {}),
      ...(emailReminder ? { emailReminder } : {}),
      ...(dateToRemind ? { dateToRemind } : {}),
      type,
      licenseeId,
      state: "active",
      addedBy: userId,
      whenAdded: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    };

    const updatedVesselCertificateRecord = {
      ref: operation.makeRef("crewCertificates"),
      data,
    };

    //TODO Create a new category if it doesn't exist
    const licenseeTouched = this.licenseeTouched(operation, licenseeId);

    return {
      ref: updatedVesselCertificateRecord.ref,
      records: [updatedVesselCertificateRecord, licenseeTouched],
    };
  }

  private licenseeTouched(operation: FirestoreOperation, licenseeId: string) {
    const licenseeData = {
      touched: operation.serverTimestamp(),
    };

    const vesselTouched = {
      ref: operation.makeRef("whenLicenseeTouched", licenseeId),
      data: licenseeData,
      options: { merge: true },
    };

    return vesselTouched;
  }
}
