import { Container } from "inversify";
import { CreateSafetyCheckUseCase } from "@src/domain/use-cases/safety/CreateSafetyCheckUseCase";
import { SafetyCheckService } from "@src/domain/services/SafetyCheckService";
import { ActionLogService } from "@src/domain/services/ActionLogService";
import { Platform } from "react-native";
import { IFirestoreService } from "@src/domain/data/IFirestoreService";
import { SERVICES } from "@src/domain/di/ServiceRegistry";
import { IFirebase } from "@src/domain/IFirebase";
import { WebFirestoreService } from "@src/domain/data/WebFirestoreService";
import { NativeFirestoreService } from "@src/domain/data/NativeFirestoreService";
import { FirebaseWeb } from "@src/domain/FirebaseWeb";
import { FirebaseNative } from "@src/domain/FirebaseNative";
import { ConsoleLogger, ILogger, SentryLogger } from "@src/domain/util/ILogger";
import { CreateIncidentUseCase } from "@src/domain/use-cases/incident/CreateIncidentUseCase";
import { UpdateIncidentUseCase } from "@src/domain/use-cases/incident/UpdateIncidentUseCase";
import { IncidentService } from "@src/domain/services/IncidentService";
import { UpdateMaintenanceHistoryUseCase } from "../use-cases/maintenance/UpdateMaintenanceHistoryUseCase";
import { MaintenanceHistoryService } from "../services/MaintenanceHistoryService";
import { CreateMaintenanceHistoryUseCase } from "../use-cases/maintenance/CreateMaintenanceHistoryUseCase";
import { CreateSafetyEquipmentExpiryUseCase } from "@src/domain/use-cases/safety/CreateSafetyEquipmentExpiryUseCase";
import { SafetyEquipmentExpiryService } from "@src/domain/services/SafetyEquipmentExpiryService";
import { UpdateSafetyEquipmentExpiryUseCase } from "@src/domain/use-cases/safety/UpdateSafetyEquipmentExpiryUseCase";
import { CompleteSafetyEquipmentExpiryUseCase } from "@src/domain/use-cases/safety/CompleteSafetyEquipmentExpiryUseCase";
import { CreateRiskUseCase } from "@src/domain/use-cases/risks/CreateRiskUseCase";
import { UpdateRiskUseCase } from "@src/domain/use-cases/risks/UpdateRiskUseCase";
import { RiskService } from "@src/domain/services/RiskService";
import { UpdateEquipmentManualsUseCase } from "../use-cases/maintenance/UpdateEquipmentManualsUseCase";
import { EquipmentManualService } from "../services/EquipmentManualService";
import { CreateEquipmentManualsUseCase } from "../use-cases/maintenance/CreateEquipmentManualsUseCase";
import { UpdateMaintenanceScheduleUseCase } from "../use-cases/maintenance/UpdateMaintenanceScheduleUseCase";
import { MaintenanceScheduleService } from "../services/MaintenanceScheduleService";
import { TagsService } from "../services/TagsService";
import { CompleteMaintenanceScheduleUseCase } from "../use-cases/maintenance/CompleteMaintenanceScheduleUseCase";
import { CreateMaintenanceScheduleUseCase } from "../use-cases/maintenance/CreateMaintenanceScheduleUseCase";
import { UpdateJobListUseCase } from "../use-cases/maintenance/UpdateJoblistUseCase";
import { JobListService } from "../services/JobListService";
import { NotificationService } from "../services/NotificationService";
import { CreateJobListUseCase } from "../use-cases/maintenance/CreateJobListUseCase";
import { UpdateJobListTaskUseCase } from "../use-cases/maintenance/UpdateJobListTaskUseCase";
import { UpdateSafetyCheckUseCase } from "@src/domain/use-cases/safety/UpdateSafetyCheckUseCase";
import { CompleteSafetyCheckUseCase } from "@src/domain/use-cases/safety/CompleteSafetyCheckUseCase";
import { UpdateSparePartUseCase } from "../use-cases/maintenance/UpdateSparePartUseCase";
import { SparePartService } from "../services/SparePartService";
import { CreateSparePartUseCase } from "../use-cases/maintenance/CreateSparePartUseCase";
import { UpdateEquipmentUseCase } from "../use-cases/maintenance/UpdateEquipmentUseCase";
import { EquipmentService } from "../services/EquipmentService";
import { CreateEquipmentUseCase } from "../use-cases/maintenance/CreateEquipmentUseCase";
import { CreateSafetyMeetingReportUseCase } from "@src/domain/use-cases/safetyMeetingReports/CreateSafetyMeetingReportUseCase";
import { UpdateSafetyMeetingReportUseCase } from "@src/domain/use-cases/safetyMeetingReports/UpdateSafetyMeetingReportUseCase.ts";
import { SafetyMeetingReportService } from "@src/domain/services/SafetyMeetingReportService";
import { CreateDangerousGoodsUseCase } from "@src/domain/use-cases/dangerousGoods/CreateDangerousGoodsUseCase";
import { UpdateDangerousGoodsUseCase } from "@src/domain/use-cases/dangerousGoods/UpdateDangerousGoodsUseCase";
import { DangerousGoodsService } from "@src/domain/services/DangerousGoodsService";
import { CreateDrillReportUseCase } from "@src/domain/use-cases/safety/CreateDrillReportUseCase";
import { UpdateDrillReportUseCase } from "@src/domain/use-cases/safety/UpdateDrillReportUseCase";
import { DrillsService } from "@src/domain/services/DrillsService";
import { UpdateAssignedDrillsUseCase } from "@src/domain/use-cases/safety/UpdateAssignedDrillsUseCase";
import { UpdateEngineHoursUseCase } from "../use-cases/engine/UpdateEngineHoursUseCase";
import { EngineService } from "../services/EngineService";
import { FileService } from "@src/domain/services/FileService";
import { UpdateVesselCertificateUseCase } from "../use-cases/vesselDocumentRegister/UpdateVesselCertificateUseCase";
import { VesselCertificateService } from "../services/VesselCertificateService";
import { CreateVesselCertificateUseCase } from "../use-cases/vesselDocumentRegister/CreateVesselCertificateUseCase";
import { RenewVesselCertificateUseCase } from "../use-cases/vesselDocumentRegister/RenewCertificateUseCase";
import { UpdateCrewCertificateUseCase } from "../use-cases/crewCertificates/UpdateCrewCertificateUseCase";
import { CrewCertificateService } from "../services/CrewCertificateService";
import { CreateCrewCertificateUseCase } from "../use-cases/crewCertificates/CreateCrewCertificateUseCase";
import { RenewCrewCertificateUseCase } from "../use-cases/crewCertificates/RenewCrewCertificateUseCase";

export const ServiceContainer = new Container();
// Use cases
ServiceContainer.bind(CreateSafetyCheckUseCase).toSelf();
ServiceContainer.bind(UpdateSafetyCheckUseCase).toSelf();
ServiceContainer.bind(CompleteSafetyCheckUseCase).toSelf();

ServiceContainer.bind(CreateSafetyEquipmentExpiryUseCase).toSelf();
ServiceContainer.bind(UpdateSafetyEquipmentExpiryUseCase).toSelf();
ServiceContainer.bind(CompleteSafetyEquipmentExpiryUseCase).toSelf();

ServiceContainer.bind(CreateDrillReportUseCase).toSelf();
ServiceContainer.bind(UpdateDrillReportUseCase).toSelf();
ServiceContainer.bind(UpdateAssignedDrillsUseCase).toSelf();

ServiceContainer.bind(CreateIncidentUseCase).toSelf();
ServiceContainer.bind(UpdateIncidentUseCase).toSelf();

ServiceContainer.bind(UpdateMaintenanceHistoryUseCase).toSelf();
ServiceContainer.bind(CreateMaintenanceHistoryUseCase).toSelf();

ServiceContainer.bind(CreateRiskUseCase).toSelf();
ServiceContainer.bind(UpdateRiskUseCase).toSelf();
ServiceContainer.bind(UpdateEquipmentManualsUseCase).toSelf();
ServiceContainer.bind(CreateEquipmentManualsUseCase).toSelf();
ServiceContainer.bind(UpdateMaintenanceScheduleUseCase).toSelf();
ServiceContainer.bind(CompleteMaintenanceScheduleUseCase).toSelf();
ServiceContainer.bind(CreateMaintenanceScheduleUseCase).toSelf();
ServiceContainer.bind(UpdateJobListUseCase).toSelf();
ServiceContainer.bind(CreateJobListUseCase).toSelf();
ServiceContainer.bind(UpdateJobListTaskUseCase).toSelf();
ServiceContainer.bind(UpdateSparePartUseCase).toSelf();
ServiceContainer.bind(CreateSparePartUseCase).toSelf();
ServiceContainer.bind(UpdateEquipmentUseCase).toSelf();
ServiceContainer.bind(CreateEquipmentUseCase).toSelf();
ServiceContainer.bind(CreateSafetyMeetingReportUseCase).toSelf();
ServiceContainer.bind(UpdateSafetyMeetingReportUseCase).toSelf();
ServiceContainer.bind(CreateDangerousGoodsUseCase).toSelf();
ServiceContainer.bind(UpdateDangerousGoodsUseCase).toSelf();
ServiceContainer.bind(UpdateEngineHoursUseCase).toSelf();
ServiceContainer.bind(UpdateVesselCertificateUseCase).toSelf();
ServiceContainer.bind(CreateVesselCertificateUseCase).toSelf();
ServiceContainer.bind(RenewVesselCertificateUseCase).toSelf();
ServiceContainer.bind(UpdateCrewCertificateUseCase).toSelf();
ServiceContainer.bind(CreateCrewCertificateUseCase).toSelf();
ServiceContainer.bind(RenewCrewCertificateUseCase).toSelf();

// Services
ServiceContainer.bind(SafetyCheckService).toSelf();
ServiceContainer.bind(SafetyEquipmentExpiryService).toSelf();
ServiceContainer.bind(DrillsService).toSelf();
ServiceContainer.bind(ActionLogService).toSelf();
ServiceContainer.bind(IncidentService).toSelf();
ServiceContainer.bind(MaintenanceHistoryService).toSelf();
ServiceContainer.bind(RiskService).toSelf();
ServiceContainer.bind(EquipmentManualService).toSelf();
ServiceContainer.bind(MaintenanceScheduleService).toSelf();
ServiceContainer.bind(TagsService).toSelf();
ServiceContainer.bind(JobListService).toSelf();
ServiceContainer.bind(NotificationService).toSelf();
ServiceContainer.bind(SparePartService).toSelf();
ServiceContainer.bind(EquipmentService).toSelf();
ServiceContainer.bind(SafetyMeetingReportService).toSelf();
ServiceContainer.bind(DangerousGoodsService).toSelf();
ServiceContainer.bind(EngineService).toSelf();
ServiceContainer.bind(VesselCertificateService).toSelf();
ServiceContainer.bind(FileService).toSelf();
ServiceContainer.bind(CrewCertificateService).toSelf();

// Utils
ServiceContainer.bind<ILogger>(SERVICES.ILogger).toConstantValue(
  new ConsoleLogger(),
); // TODO - Change out implementation based on environment
// ServiceContainer.bind<ILogger>(SERVICES.ILogger).to(SentryLogger); // TODO - Change out implementation based on environment

// // Platform specific
ServiceContainer.bind<IFirestoreService>(SERVICES.IFirestoreService)
  .to(WebFirestoreService)
  .inSingletonScope()
  .when(() => Platform.OS === "web");

ServiceContainer.bind<IFirestoreService>(SERVICES.IFirestoreService)
  .to(NativeFirestoreService) // TODO
  .inSingletonScope()
  .when(() => Platform.OS !== "web");

ServiceContainer.bind<IFirebase>(SERVICES.IFirebase)
  .to(FirebaseWeb)
  .inSingletonScope()
  .when(() => Platform.OS === "web");

ServiceContainer.bind<IFirebase>(SERVICES.IFirebase)
  .to(FirebaseNative)
  .inSingletonScope()
  .when(() => Platform.OS !== "web");

export function getFirestoreService(): IFirestoreService {
  return ServiceContainer.get<IFirestoreService>(SERVICES.IFirestoreService);
}
